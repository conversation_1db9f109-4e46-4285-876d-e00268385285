#include "file.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <libgen.h>
#include <sys/stat.h>

// 定义全局变量
char g_program_dir[PATH_MAX] = "/home/<USER>";
char g_datafile_dir[PATH_MAX] = {0};

// 动态路径变量定义
char DIC_FOLDER[PATH_MAX] = {0};
char IMAGE_LIST_FILE[PATH_MAX] = {0};
char CAPTURE_SINGLE[PATH_MAX] = {0};
char HOST_PORT_FILE[PATH_MAX] = {0};
char POINT_LIST_FILE[PATH_MAX] = {0};
char IMAGE_FOLDER[PATH_MAX] = {0};
char MASTER_IMAGE_FOLDER[PATH_MAX] = {0};
char MULTIMASTER_IMAGE_FOLDER[PATH_MAX] = {0};
char CHANGE_DATA_FILE[PATH_MAX] = {0};
char VIDEO_DISPLACEMENT_FILE[PATH_MAX] = {0};
char LOG_FILE[PATH_MAX] = {0};
char ERROR_LOG_FILE[PATH_MAX] = {0};
char mqttDemo[PATH_MAX] = {0};
char HIMIX_PUB_LOG_FILE[PATH_MAX] = {0};
char LOG_DIR[PATH_MAX] = {0};
char CFG_INI[PATH_MAX] = {0};
char ACTIVATION_FILE[PATH_MAX] = {0};
char DB_PATH[PATH_MAX] = {0};
char REBOOT_SCRIPT[PATH_MAX] = {0};

// 创建目录（如果不存在）
static int create_directory_if_not_exists(const char *path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        // 目录不存在，尝试创建
        char cmd[PATH_MAX + 20];
        snprintf(cmd, sizeof(cmd), "mkdir -p \"%s\"", path);
        return system(cmd);
    }
    return 0; // 目录已存在
}

// 获取程序所在目录
static int get_program_directory(char *prog_dir, size_t size) {
    char exe_path[PATH_MAX];
    ssize_t len = readlink("/proc/self/exe", exe_path, sizeof(exe_path) - 1);
    
    if (len == -1) {
        // 如果readlink失败，尝试使用argv[0]的方法
        return -1;
    }
    
    exe_path[len] = '\0';
    char *dir = dirname(exe_path);
    
    if (strlen(dir) >= size) {
        return -1; // 缓冲区太小
    }
    
    strcpy(prog_dir, dir);
    return 0;
}

// 初始化文件路径
void init_file_paths(void) {
    // 获取程序所在目录
    if (get_program_directory(g_program_dir, sizeof(g_program_dir)) != 0) {
        // 如果获取失败，使用当前目录
        strcpy(g_program_dir, ".");
    }
    
    // 构建datafile目录路径（程序目录的上级目录下的datafile）
    snprintf(g_datafile_dir, sizeof(g_datafile_dir),  g_program_dir);
    
    // 构建所有路径
    snprintf(DIC_FOLDER, sizeof(DIC_FOLDER), "%s/", g_datafile_dir);
    snprintf(IMAGE_LIST_FILE, sizeof(IMAGE_LIST_FILE), "%s/orig/originalObservation.txt", g_datafile_dir);
    snprintf(CAPTURE_SINGLE, sizeof(CAPTURE_SINGLE), "%s/capture_single", g_program_dir);
    snprintf(HOST_PORT_FILE, sizeof(HOST_PORT_FILE), "%s/dic/para/parameters.txt", g_datafile_dir);
    snprintf(POINT_LIST_FILE, sizeof(POINT_LIST_FILE), "%s/dic/para/points_list.txt", g_datafile_dir);
    snprintf(IMAGE_FOLDER, sizeof(IMAGE_FOLDER), "%s/orig/", g_datafile_dir);
    snprintf(MASTER_IMAGE_FOLDER, sizeof(MASTER_IMAGE_FOLDER), "%s/dic/para/master/", g_datafile_dir);
    snprintf(MULTIMASTER_IMAGE_FOLDER, sizeof(MULTIMASTER_IMAGE_FOLDER), "%s/dic/para/multiMaster/", g_datafile_dir);
    snprintf(CHANGE_DATA_FILE, sizeof(CHANGE_DATA_FILE), "%s/dic/para/points_displacement.txt", g_datafile_dir);
    snprintf(VIDEO_DISPLACEMENT_FILE, sizeof(VIDEO_DISPLACEMENT_FILE), "%s/dic/para/video_displacement.txt", g_datafile_dir);
    snprintf(LOG_FILE, sizeof(LOG_FILE), "%s/dic/logs/visual_log.txt", g_datafile_dir);
    snprintf(ERROR_LOG_FILE, sizeof(ERROR_LOG_FILE), "%s/dic/logs/visual_error.txt", g_datafile_dir);
    snprintf(mqttDemo, sizeof(mqttDemo), "%s/mqttDemo", g_program_dir);
    snprintf(HIMIX_PUB_LOG_FILE, sizeof(HIMIX_PUB_LOG_FILE), "%s/dic/logs/himixPub.log", g_datafile_dir);
    snprintf(LOG_DIR, sizeof(LOG_DIR), "%s/dic/logs", g_datafile_dir);
    snprintf(CFG_INI, sizeof(CFG_INI), "%s/cfg.ini", g_program_dir);
    snprintf(ACTIVATION_FILE, sizeof(ACTIVATION_FILE), "%s/activation.dat", g_program_dir);
    snprintf(DB_PATH, sizeof(DB_PATH), "%s/dic/result.db", g_datafile_dir);
    snprintf(REBOOT_SCRIPT, sizeof(REBOOT_SCRIPT), "%s/runMQTT.sh", g_program_dir);
    
    // 创建必要的目录
    char temp_dir[PATH_MAX];
    
    // 创建datafile目录
    create_directory_if_not_exists(g_datafile_dir);
    
    // 创建orig目录
    snprintf(temp_dir, sizeof(temp_dir), "%s/orig", g_datafile_dir);
    create_directory_if_not_exists(temp_dir);
    
    // 创建dic目录
    snprintf(temp_dir, sizeof(temp_dir), "%s/dic", g_datafile_dir);
    create_directory_if_not_exists(temp_dir);
    
    // 创建para目录
    snprintf(temp_dir, sizeof(temp_dir), "%s/dic/para", g_datafile_dir);
    create_directory_if_not_exists(temp_dir);
    
    // 创建master目录
    snprintf(temp_dir, sizeof(temp_dir), "%s/dic/para/master", g_datafile_dir);
    create_directory_if_not_exists(temp_dir);
    
    // 创建multiMaster目录
    snprintf(temp_dir, sizeof(temp_dir), "%s/dic/para/multiMaster", g_datafile_dir);
    create_directory_if_not_exists(temp_dir);
    
    // 创建logs目录
    create_directory_if_not_exists(LOG_DIR);
}
