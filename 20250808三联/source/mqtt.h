#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <unistd.h>
#include <pthread.h>
#include <linux/reboot.h>
#include "mosquitto.h"
#include "cJSON.h"
#include "fio.h"
#include "tbase64.h"
#include "MQTTClient.h"
#include <sqlite3.h>
#include "tcp.h"
#include <time.h>
#include <stdarg.h>
#include "activation.h"
#include "file.h"
#include "camera_ssh.h"
// 全局变量声明
extern int activated_status;
extern struct tcp_client *g_tcp_client;
extern DeviceConfig g_device_config;

// 用于TCP线程的数据结构
struct tcp_thread_data {
    struct tcp_client *client;
    char *message;
    char *type;
    char *temp;
};


char *DEVICE_ID = NULL;
char UP_TOPIC[50] = "up/";
char DOWN_TOPIC[50] = "down/";
char RESPOND_TOPIC[50] = "respond/";
char *HOST = NULL;
int PORT;
int TCP_PORT;
// 0:mqtt,1:tcp     
int image_net_model = 0;  // 使用默认值0初始化，后续再更新


// 在文件开头添加宏定义
#define TYPE_MAX_LEN 128  // 增加类型字符串的最大长度
#define BASE64_MAX_SIZE (10 * 1024 * 1024)  // 限制base64编码后的最大大小为10MB
#define MAX_ROUND_STR 32  // 增加缓冲区大小以适应更大的数字

// TCP线程函数 - 处理TCP发送
void *tcp_thread_function(void *arg) {
    struct tcp_thread_data *data = (struct tcp_thread_data *)arg;
    
    // 在独立线程中执行TCP发送
    cJSON *json_obj = cJSON_CreateObject();
    if (!json_obj) {
        print_error_with_time("创建JSON对象失败\n");
        goto cleanup;
    }

    // 添加JSON字段
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", data->type);
    cJSON_AddStringToObject(json_obj, "data", data->message);
    //存在温度数据
        // 仅在温度数据存在时添加
        if (data->temp != NULL) {
            cJSON_AddStringToObject(json_obj, "temp", data->temp);
        }

    // 转换为字符串
    char *json_string = cJSON_Print(json_obj);
    if (!json_string) {
        print_error_with_time("JSON转换失败\n");
        cJSON_Delete(json_obj);
        goto cleanup;
    }
    // 发送数据，增加重试机制
    int max_retries = 3;
    bool sent = false;
    struct tcp_client *thread_client = data->client;
    for (int i = 0; i < max_retries && !sent; i++) {
        // 确保连接已建立
        if (!thread_client->connected && !tcp_client_connect(thread_client)) {
            print_error_with_time("TCP线程: 连接失败，重试 %d/%d\n", i+1, max_retries);
            sleep(1); // 添加延迟后重试
            continue;
        } 
        // 尝试发送数据
        if (tcp_client_send(thread_client, json_string)) {
            sent = true;
        } else {
            print_error_with_time("TCP线程: 发送失败，重试 %d/%d\n", i+1, max_retries);
            // 断开连接以便重新尝试
            tcp_client_disconnect(thread_client);
            sleep(1); // 添加延迟后重试
        }
    }

    if (!sent) {
        print_error_with_time("TCP线程: 发送彻底失败，放弃尝试\n");
    }

    // 清理资源
    free(json_string);
    cJSON_Delete(json_obj);

cleanup:
    // 释放资源
    tcp_client_disconnect(thread_client);
    free(thread_client); // 释放线程专用的TCP客户端副本
    free(data->message);
    free(data->type);
    free(data->temp);
    free(data);
    return NULL;
}
// 订阅程序--连接服务器成功的回调
void sub_connect_callback(struct mosquitto *mosq, void *obj, int rc)
{

        if(rc){
                print_with_time("on_connect error!\n");
        }else{
                int ret = mosquitto_subscribe(mosq, NULL, DOWN_TOPIC, 2);
                if(ret != MOSQ_ERR_SUCCESS)
                    print_with_time("Could not subscribe to topic. Return code: %d\n", ret);
        }
}

// 订阅程序--连接服务器失败的回调
void sub_disconnect_callback(struct mosquitto *mosq, void *obj, int rc)
{
        print_with_time("MQTT断开连接原因: %d \n", rc);
        char resolved_path[PATH_MAX];
        char *real_path = realpath(mqttDemo, resolved_path);
        if (real_path == NULL) {
            print_error_with_time("realpath failed");
            return;
        }
        char port[4];
        sprintf(port, "%d", PORT);
        char *args[] = {real_path, DEVICE_ID, HOST, port, NULL};
        char *env[] = {"LD_LIBRARY_PATH=/usrappfs/lib:$LD_LIBRARY_PATH", NULL};

        if (execve(real_path, args, env) == -1) {
            print_error_with_time("execve failed");
        }
}

void subscribe_callback(struct mosquitto *mosq, void *obj, int mid, int qos_count, const int *granted_qos)
{
    print_with_time("Call the function: on_subscribe\n");
}

void publish_callback(struct mosquitto *mosq, void *obj, int rc)
{
    // print_with_time("Call the function: publish_callback\n");
}

// 互斥锁初始化
pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER;
// 遍历mqtt服务器,发布消息
bool publish_msg(void* arg, struct mosquitto *mosq, const char* type, char* message, size_t msglen, char* other, size_t otherlen, char* round)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);

    // 如果发布的是图片数据，则发布数据中含有图片时间、名称信息
    if(strcmp(type,IMAGE_TYPE)==0)
    {
        cJSON_AddNumberToObject(json_obj, "imageinfolen", otherlen);
        cJSON_AddStringToObject(json_obj, "imageinfo", other);
        cJSON_AddStringToObject(json_obj, "round", round);
    }
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);

    // 不需要重新连接，使用已有的连接
    
    pthread_mutex_lock(&mutex);
    
    /*发布消息*/
      mosquitto_publish(mosq, NULL, UP_TOPIC, strlen(json_string)+1, json_string, 0, 0);

    pthread_mutex_unlock(&mutex);

    // 释放内存
    if(message)
        memset(message, 0, sizeof(message));
    if(json_string)
        memset(json_string, 0, sizeof(json_string));

    return true;
}
// 添加可靠发布函数，带有重试机制
bool reliable_publish(struct mosquitto *mosq, const char* topic, void* payload, int payload_len, int qos) {
    int max_retries = 3;
    int retry_count = 0;
    int result;
    bool success = false;
    
    while (retry_count < max_retries && !success) {
        pthread_mutex_lock(&mutex);
        result = mosquitto_publish(mosq, NULL, topic, payload_len, payload, qos, 0);
        pthread_mutex_unlock(&mutex);
        
        if (result == MOSQ_ERR_SUCCESS) {
            success = true;
        } else {
            retry_count++;
            print_error_with_time("MQTT发布失败 (尝试 %d/%d): %s\n", 
                                 retry_count, max_retries, mosquitto_strerror(result));
            
            // 添加退避延迟
            usleep(100000 * retry_count); // 递增延迟: 100ms, 200ms, 300ms
        }
    }
    
    return success;
}
//增加可靠函数获取，到达状态用于发送图片
bool publish_msg_(void* arg, struct mosquitto *mosq, const char* type, char* message, size_t msglen, char* other, size_t otherlen, char* round)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    if (!json_obj) {
        print_error_with_time("创建JSON对象失败\n");
        return false;
    }
    
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);

    // 如果发布的是图片数据，则发布数据中含有图片时间、名称信息
    if(strcmp(type,IMAGE_TYPE)==0)
    {
        cJSON_AddNumberToObject(json_obj, "imageinfolen", otherlen);
        cJSON_AddStringToObject(json_obj, "imageinfo", other);
        cJSON_AddStringToObject(json_obj, "round", round);
    }
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);
    if (!json_string) {
        print_error_with_time("JSON转换失败\n");
        cJSON_Delete(json_obj);
        return false;
    }

    // 使用可靠发布函数代替原来的直接发布
    bool success = reliable_publish(mosq, UP_TOPIC, json_string, strlen(json_string)+1, 1);
    
    if (success) {
        print_with_time("消息发布成功: 类型=%s, 数据长度=%zu\n", type, msglen);
    } else {
        print_error_with_time("消息发布失败: 类型=%s, 数据长度=%zu\n", type, msglen);
    }

    // 正确释放内存
    cJSON_Delete(json_obj);
    free(json_string);
    
    return success;
}
// 添加网络状态检测函数
bool check_network_quality(const char *host) {
    int ping_result = -1;
    char cmd[256];
    
    // 使用ping命令检测网络连接质量，只发送2个包
    snprintf(cmd, sizeof(cmd), "ping -c 2 -W 1 %s > /dev/null 2>&1", host);
    ping_result = system(cmd);
    
    return (ping_result == 0);
}



// 修改publish_image_temp_base64函数使用可靠发布
void publish_image_temp_base64(struct mosquitto *mosq, char* type, char* round, size_t msglen, char* message, char* temp) {
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    if (!json_obj) {
        print_error_with_time("创建JSON对象失败\n");
        return;
    }
    
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);
    cJSON_AddStringToObject(json_obj, "round", round);
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);
    
    // 判断温度值是否有效
    if (temp != NULL && strlen(temp) > 0) {
       cJSON_AddStringToObject(json_obj, "temp", temp);
    }

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);
    if (!json_string) {
        print_error_with_time("JSON转换失败\n");
        cJSON_Delete(json_obj);  // 确保在错误情况下也释放json_obj
        return;
    }
    
    // 使用可靠发布函数，设置QoS为1 (至少发送一次)
    bool success = reliable_publish(mosq, RESPOND_TOPIC, json_string, strlen(json_string)+1, 1);
    
    if (!success) {
        print_error_with_time("分块发送失败，数据可能不完整\n");
    }
    
    // 确保释放内存
    cJSON_Delete(json_obj);
    free(json_string);
}

// 修改publish_image_base64函数使用可靠发布
void publish_image_base64(struct mosquitto *mosq, char* type, char* round, size_t msglen, char* message)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);
    cJSON_AddStringToObject(json_obj, "round", round);
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);
    if (!json_string) {
        print_error_with_time("JSON转换失败\n");
        cJSON_Delete(json_obj);
        return;
    }

    // 使用可靠发布函数，设置QoS为1
    bool success = reliable_publish(mosq, RESPOND_TOPIC, json_string, strlen(json_string)+1, 1);
    
    if (!success) {
        print_error_with_time("分块发送失败，数据可能不完整\n");
    }
    
    // 确保释放内存
    cJSON_Delete(json_obj);
    free(json_string);
}

// 修改 publish_tcp_client_send 函数，使用线程方式
void publish_tcp_client_send(struct tcp_client *client, const char *message, char* type, char* temp) {
    // 创建TCP线程数据
    struct tcp_thread_data *data = malloc(sizeof(struct tcp_thread_data));
    if (!data) {
        print_error_with_time("创建TCP线程数据结构失败\n");
        return;
    }
    
    // 创建TCP客户端的副本，避免干扰主线程的客户端
    data->client = malloc(sizeof(struct tcp_client));
    if (!data->client) {
        print_error_with_time("创建TCP客户端副本失败\n");
        free(data);
        return;
    }
    
    // 复制TCP客户端数据
    memcpy(data->client, client, sizeof(struct tcp_client));
    data->client->connected = false; // 确保线程会重新连接
    
    // 复制消息和类型
    data->message = strdup(message);
    data->type = strdup(type);
    data->temp = temp ? strdup(temp) : NULL;  // 如果有温度则复制，否则为NULL

    if (!data->message || !data->type) {
        print_error_with_time("复制消息或类型失败\n");
        if (data->message) free(data->message);
        if (data->type) free(data->type);
        if (data->temp) free(data->temp);
        free(data->client);
        free(data);
        return;
    }
    
    // 创建并启动线程
    pthread_t thread;
    if (pthread_create(&thread, NULL, tcp_thread_function, data) != 0) {
        print_error_with_time("创建TCP线程失败\n");
        free(data->message);
        free(data->type);
        free(data->client);
        free(data);
        return;
    }
    
    // 设置线程为分离状态，让其自行清理资源
    pthread_detach(thread);
}
void message_callback(struct mosquitto *mosq, void *obj, const struct mosquitto_message *message) {
    // 获取 tcp_client 实例
    struct tcp_client *client = (struct tcp_client *)obj;  // 从 obj 参数中获取 client
    
    // 在这里处理接收到的 MQTT 消息
    cJSON *json = cJSON_Parse((char *)message->payload);
    if (!json) {
        print_error_with_time("JSON解析失败: %s\n", (char *)message->payload);
        return;
    }
    
    cJSON *code = cJSON_GetObjectItemCaseSensitive(json, "code");
    cJSON *data = cJSON_GetObjectItemCaseSensitive(json, "data");
    char *base64_encoded_data = NULL;
    char *input_data = NULL;
    char type[TYPE_MAX_LEN] = {0};  // 初始化为0
    size_t base64_size = 0, input_length = 0;
    char temp_char[32]="0";//默认温度
    print_with_time("接收到消息: %s\n", (char *)message->payload);
    
    // 处理完成后务必释放cJSON对象
    bool json_processed = false;
    
    if (cJSON_IsString(code))
    {
        if (strlen(code->valuestring) >= TYPE_MAX_LEN) {
            print_with_time("错误：类型字符串太长\n");
            return;
        }
        strncpy(type, code->valuestring, TYPE_MAX_LEN - 1);
        type[TYPE_MAX_LEN - 1] = '\0';  // 确保字符串结束
        if(strcmp(code->valuestring,"iplist")==0){
        	strcpy(type, "iplist");
        	input_data = read_file(HOST_PORT_FILE);
        }    	
	    else if(strcmp(code->valuestring,"imagelist")==0){
	    	strcpy(type, "imagelist");
    
	    	input_data = read_file(IMAGE_LIST_FILE);
	    }
	    else if(strcmp(code->valuestring,"pointlist")==0){
	    	strcpy(type, "pointlist");
      
	    	input_data = read_file(POINT_LIST_FILE);
	    }
	    else if(strcmp(code->valuestring,"getimage")==0)
	    {
	    	strcpy(type, "getimage");
	    	char image_path[256];
	    	strcpy(image_path, IMAGE_FOLDER);
	    	// 检查data是否有效
	    	if (!data || !cJSON_IsString(data) || !data->valuestring || strcmp(data->valuestring, "") == 0) {
	    		input_data = "请指定要获取的图像文件名！";
	    		print_error_with_time("getimage命令缺少有效的文件名\n");
	    		return;
	    	}
	    	
	    	// 安全地拼接路径
	    	strcat(image_path, data->valuestring);

	    	
	    	// 检查文件是否存在
	    	if (access(image_path, F_OK) != 0) {
	    		print_error_with_time("图片文件不存在: %s\n", image_path);
	    		input_data = "找不到指定的图像！";
	    		return;
	    	}
	    	
	    	// 检查文件是否可读
	    	if (access(image_path, R_OK) != 0) {
	    		print_error_with_time("图片文件无法读取: %s\n", image_path);
	    		input_data = "图像文件无法读取！";
	    		return;
	    	}
	    	

	    	base64_encoded_data = image_to_base64(image_path, &base64_size);
	    	if(!base64_encoded_data) {
	    		print_error_with_time("Base64转换失败: %s\n", image_path);
	    		input_data = "图像转换失败！";
	    	} 
	    }
	    else if(strcmp(code->valuestring,"setmasterimage")==0)
	    {
	    	strcpy(type, "setmasterimage");
    		char from_image[256], to_image[256];
    		strcpy(from_image, IMAGE_FOLDER);
    		strcpy(to_image, MASTER_IMAGE_FOLDER);
            print_with_time("setmasterimage: %s\n", data->valuestring);
    		if(fopen(strcat(from_image, data->valuestring), "rb")==NULL)
    			input_data = "找不到图像！";
    		else
	    		if(remove_all_file(MASTER_IMAGE_FOLDER))
		    		if(copy_image(from_image, strcat(to_image, data->valuestring)))
		    			// 主影像设置成功，返回主影像文件名
		    			input_data = get_first_file_name(MASTER_IMAGE_FOLDER);
	    	if(!input_data)
	    		input_data = "主影像设置失败！";		    	
	    }
	    else if(strcmp(code->valuestring,"getmasterimage")==0)
	    {
	    	
	    	strcpy(type, "getmasterimage");
	    	char image_path[256];
	    	strcpy(image_path, MASTER_IMAGE_FOLDER);
			// 不带data的getmasterimage用于获取主影像文件名，带data的用于获取主影像图像
	    	if(data==NULL)
	    		input_data = get_first_file_name(image_path);
	    	else
	    		base64_encoded_data = image_to_base64(strcat(image_path, data->valuestring), &base64_size);
	    }
        // else if(strcmp(code->valuestring,"getmastername")==0)
        // {
        //     strcpy(type, "getmastername");
        //     char image_path[256] = MASTER_IMAGE_FOLDER;
        //     input_data = get_first_file_name(image_path);
        // }
	    else if(strcmp(code->valuestring,"setpoint")==0)
	    {
	    	strcpy(type, "setpoint");
	    	if(write_last_row_include_index(POINT_LIST_FILE, data->valuestring))
	    		input_data = "测点添加成功！";
	    	else
	    		input_data = "测点添加失败！";
	    }
	    else if(strcmp(code->valuestring,"alterpoint")==0)
	    {
	    	strcpy(type, "alterpoint");
	    	if(write_file(POINT_LIST_FILE, data->valuestring))
	    		input_data = "测点列表修改成功！";
	    	else
	    		input_data = "测点列表修改失败！";
	    }
	    else if(strcmp(code->valuestring,"deleteip")==0)
	    {
	    	strcpy(type, "deleteip");
	    	if(write_file(HOST_PORT_FILE, data->valuestring))
	    		input_data = "Ip删除成功！";
	    	else
	    		input_data = "IP删除失败！";
	    }
	    else if(strcmp(code->valuestring,"setip")==0)
	    {
	    	strcpy(type, "setip");
            
	    	if(write_last_row(HOST_PORT_FILE, data->valuestring))
	    		input_data = "IP添加成功！";
	    	else
	    		input_data = "IP添加失败！";
	    }
        else if(strcmp(code->valuestring,"upload")==0)
        {
            strcpy(type, "upload");
            cJSON *path = cJSON_GetObjectItemCaseSensitive(json, "path");

            size_t decoded_length = strlen(data->valuestring);
            unsigned char *decoded_data = (unsigned char *)malloc(decoded_length+1);
            
            size_t decoded_len = base64_decode_len(data->valuestring, strlen(data->valuestring), decoded_data);
            char file_path[100];

            strcat(strcpy(file_path, DIC_FOLDER), path->valuestring);

            // 检查文件是否存在
            if (access(file_path, F_OK) != -1) {
                // 文件存在，尝试删除
                remove(file_path);
            }
            
            FILE *fp = fopen(file_path, "wb");
            if (fp == NULL) {
                input_data = "文件上传失败！";
                print_error_with_time("Error opening file");
            }else{
                fwrite(decoded_data, 1, decoded_len, fp);
                fclose(fp);
                input_data = "文件上传成功！";
            }
        }
        else if(strcmp(code->valuestring,"subimagelist")==0)
        {
            strcpy(type, "subimagelist");
            // char *concatenated = concatenate_files(MULTIMASTER_IMAGE_FOLDER);
            input_data = concatenate_files(MULTIMASTER_IMAGE_FOLDER);
            // input_data = "获取辅影像列表";
            print_with_time("%s\n", input_data);
        }
        else if(strcmp(code->valuestring,"getsubimage")==0)
        {
            strcpy(type, "getsubimage");
            char image_path[256];
            strcpy(image_path, MULTIMASTER_IMAGE_FOLDER);
            
            // 检查data是否有效
            if (!data || !cJSON_IsString(data) || !data->valuestring || strcmp(data->valuestring, "") == 0) {
                input_data = "请指定辅影像文件名！";
                print_error_with_time("getsubimage命令缺少有效的文件名\n");
            } else {
                strcat(image_path, data->valuestring);
                // 检查文件是否存在
                if (access(image_path, F_OK) != 0) {
                    print_error_with_time("辅影像文件不存在: %s\n", image_path);
                    input_data = "找不到指定的辅影像！";
                } else if (access(image_path, R_OK) != 0) {
                    print_error_with_time("辅影像文件无法读取: %s\n", image_path);
                    input_data = "辅影像文件无法读取！";
                } else {
                    base64_encoded_data = image_to_base64(image_path, &base64_size);
                    if(!base64_encoded_data) {
                        input_data = "辅影像转换失败！";
                    }
                }
            }
        }
        else if(strcmp(code->valuestring,"setsubimage")==0)
        {
            strcpy(type, "setsubimage");
            char from_image[256], to_image[256];
            strcpy(from_image, IMAGE_FOLDER);
            strcpy(to_image, MULTIMASTER_IMAGE_FOLDER);
            
            // 检查data是否有效
            if (!data || !cJSON_IsString(data) || !data->valuestring || strcmp(data->valuestring, "") == 0) {
                input_data = "请指定要设置的辅影像文件名！";
                print_error_with_time("setsubimage命令缺少有效的文件名\n");
            } else {
                strcat(from_image, data->valuestring);
                
                if(access(from_image, F_OK) != 0) {
                    input_data = "找不到指定的图像！";
                    print_error_with_time("源图像不存在: %s\n", from_image);
                } else if(access(from_image, R_OK) != 0) {
                    input_data = "图像文件无法读取！";
                    print_error_with_time("源图像无法读取: %s\n", from_image);
                } else {
                    strcat(to_image, data->valuestring);
                    // 检查文件是否已存在
                    if (access(to_image, F_OK) == 0) {
                        input_data = "图像已存在！";
                    } else if(copy_image(from_image, to_image)) {
                        input_data = "图像设置成功！";
                    } else {
                        input_data = "图像复制失败！";
                    }
                }
            }
            
            // 如果执行到此处还没有设置input_data，说明有错误
            if(!input_data) {
                input_data = "图像设置失败！";
            }
        }
        else if(strcmp(code->valuestring,"delsubimage")==0)
        {
            strcpy(type, "delsubimage");
            char file_path[100];
            strcat(strcpy(file_path, MULTIMASTER_IMAGE_FOLDER), data->valuestring);

            // 检查文件是否存在
            if (access(file_path, F_OK) != -1) {
                // 删除文件
                if (remove(file_path) == 0) {
                    input_data = "图像删除成功！";
                } else {
                    input_data = "图像删除失败！";
                }
            }else
                input_data = "找不到图像！";
        }
        else if(strcmp(code->valuestring,"getsizelog")==0)
        {
            // 获取文件大小日志
            strcpy(type, "getsizelog");
            input_data = get_file_size(LOG_FILE);
          
        }
        else if(strcmp(code->valuestring,"downlog")==0)
        {
            // 下载日志
            strcpy(type, "downlog");
            input_data = read_file(LOG_FILE);
           
        }
        else if(strcmp(code->valuestring,"downlog50")==0)
        {
            // 获取最后50行日志
            strcpy(type, "downlog50");
            input_data = read_last_50_lines(LOG_FILE);
            if (!input_data) {
                input_data = strdup("Failed to read log file");
            }
        }
        else if(strcmp(code->valuestring,"clearlog")==0)
        {
            // 清除日志文件
            strcpy(type, "clearlog");
            
            if(clear_file(LOG_FILE)) {
                input_data = "日志清除成功！";
            } else {
                input_data = "日志清除失败！";
            }
        }
        else if(strcmp(code->valuestring,"getsizedb")==0)
        {
            // 获取数据文件大小
            strcpy(type, "getsizedb");
            input_data = get_file_size(DB_PATH);
        }
        else if(strcmp(code->valuestring,"downdb100")==0)
        {
            // 获取最近100条记录
            strcpy(type, "downdb100");
            input_data = read_db_records(DB_PATH, 100);
            if (!input_data) {
                input_data = "获取数据失败";
            }
        }
        else if(strcmp(code->valuestring,"downdb")==0)
        {
            // 获取所有记录
            strcpy(type, "downdb");
            input_data = read_db_records(DB_PATH, 0);
            if (!input_data) {
                input_data = "获取数据失败";
            }
        }
        else if(strcmp(code->valuestring,"cleardb")==0)
        {
            // 清空数据库表
            strcpy(type, "cleardb");
            if (clear_db_table(DB_PATH)) {
                input_data = "数据库清除成功！";
            } else {
                input_data = "数据库清除失败！";
            }
        }
        else if(strcmp(code->valuestring,"reboot")==0)
        {
            // 设备重启
            strcpy(type, "reboot");
            reboot_device();
            
        }
        else if(strcmp(code->valuestring,"syncdb")==0)
        {
            // 同步数据库
            strcpy(type, "syncdb");
             // 解析JSON中的时间范围
            cJSON *startTime = cJSON_GetObjectItemCaseSensitive(data, "startTime");
            cJSON *endTime = cJSON_GetObjectItemCaseSensitive(data, "endTime");
           if (!startTime || !endTime || !startTime->valuestring || !endTime->valuestring) {
              input_data = "时间格式错误！";
    } else {
            char* result = sync_db(startTime, endTime);
            input_data = result;
         }

        }
        else if(strcmp(code->valuestring,"sshtunnel")==0)
        {
            // 开启反向代理
            strcpy(type, "sshtunnel");
            int ret = start_ssh_tunnel();
            if(ret == 0)
                input_data = "SSH隧道开启失败！";
            else
                input_data = "SSH隧道开启成功！";
        }
        else if(strcmp(code->valuestring,"capture")==0)
        {
            // 抓拍照片
            strcpy(type, "capture");
            // 调用抓拍函数，无需参数使用NULL
            bool ret = take_photo_with_capture_single(NULL);
            if (ret) {
                print_with_time("照片抓取成功，主动重置MQTT连接\n");
                mosquitto_disconnect(mosq);
                sleep(1);  
                int reconnect_result = mosquitto_reconnect(mosq);
                if (reconnect_result == MOSQ_ERR_SUCCESS) {
                    print_with_time("MQTT连接已重置\n");
                } else {
                    print_error_with_time("MQTT重连失败: %s\n", mosquitto_strerror(reconnect_result));
                }
            }
            if (ret)
            {
                // 抓拍成功，读取最后一行获取照片名和温度
                FILE *fp = fopen(IMAGE_LIST_FILE, "r");
                if (fp == NULL)
                {
                    input_data = "抓拍成功，但无法读取照片信息文件！";
                }
                else
                {
                    char line[MAX_SIZE] = {0};
                    char last_line[MAX_SIZE] = {0};

                    // 读取最后一行
                    while (fgets(line, sizeof(line), fp) != NULL)
                    {
                        strcpy(last_line, line);
                    }
                    fclose(fp);
                    print_with_time("最后一行: %s\n", last_line);
                    if (strlen(last_line) > 0)
                    {
                        // 解析最后一行，获取照片名称和温度
                        char timestamp[MAX_SIZE] = {0};
                        char photo_name[MAX_SIZE] = {0};


                        // 使用sscanf解析三个字段: 时间戳、照片名称和温度
                        int matched = sscanf(last_line, "%s %s %s", timestamp, photo_name, temp_char);

                        if (matched < 2)
                        {
                            // 至少需要解析出时间戳和照片名称
                            input_data = "抓拍成功，但照片信息格式不正确！";
                        }
                        else
                        {
                            print_with_time("解析照片信息 - 时间戳: %s, 照片: %s, 温度: %ss\n",
                                            timestamp, photo_name, temp_char);

                            // 构建完整的照片路径
                            char photo_path[PATH_MAX];  // 使用PATH_MAX而不是MAX_SIZE
                            snprintf(photo_path, sizeof(photo_path), "%s%s", IMAGE_FOLDER, photo_name);

                            // 使用 image_to_base64 函数直接从文件路径获取 base64 编码
                            base64_encoded_data = image_to_base64(photo_path, &base64_size);
                            if (base64_encoded_data == NULL)
                            {
                                input_data = "照片转换 base64 失败！";
                            }
                        }
                    }
                    else
                    {
                        input_data = "抓拍成功，但无法获取照片信息！";
                    }
                }
            }
            else
            {
                input_data = "抓拍失败！";
            }
        }
        else if(strcmp(code->valuestring,"getregcode")==0)
        {
            // 获取注册码
            strcpy(type, "getregcode");
            input_data = get_reg_code();
            print_with_time("注册码: %s\n", input_data);
            if(input_data == NULL)
                input_data = "获取注册码失败！";
        }
        else if(strcmp(code->valuestring,"getactivation")==0)
        {
            // 获取激活信息
            strcpy(type, "getactivation");
            // 先获取激活信息
            char *activation_info = get_activation_info();
            print_with_time("获取到的原始激活信息: %s\n", activation_info ? activation_info : "NULL");
            
            if (!activation_info || strlen(activation_info) == 0) {
                print_with_time("获取激活信息失败，使用默认值\n");
                input_data = strdup("获取激活信息失败");
            } else {
                // 为 input_data 分配新的内存并复制数据
                input_data = strdup(activation_info);
                if (!input_data) {
                    print_error_with_time("内存分配失败\n");
                    input_data = strdup("内存分配失败");
                }
                // 释放原始激活信息
                free(activation_info);
            }
        }
        else if(strcmp(code->valuestring,"activate")==0)
        {
            // 激活设备
            strcpy(type, "activate");
                // 检查 data 是否为空
    if (!data || !data->valuestring) {
        print_with_time("激活码无效\n");  // 这里是打印的来源
        input_data = "激活失败！";
        return;
    }
             // Base64 解码
    int encrypted_data_len = 0;
    unsigned char *encrypted_data = base64_decode(data->valuestring, &encrypted_data_len);

            int ret = verify_activation_code(encrypted_data,encrypted_data_len);
          // 释放内存
            free(encrypted_data);
            if(ret == 0)
                input_data = "激活失败！";
            else
                input_data = "激活成功！";
        }
        else if(strcmp(code->valuestring,"getdeviceinfo")==0)
        {
            // 获取设备信息
            strcpy(type, "getdeviceinfo");
            char *device_info = get_device_info();
            if(device_info == NULL)
                input_data = "获取设备信息失败！";
            else
                input_data = strdup(device_info);
            free(device_info);
        }

	    if(base64_encoded_data == NULL && input_data != NULL)
	    {
	    	input_length = strlen(input_data);
        	base64_encoded_data = base64_encode((const unsigned char *)input_data, input_length, &base64_size);
       
	    }

        if(base64_encoded_data){
            if (image_net_model == 1) {
                // TCP模式保持不变
                print_with_time("使用TCP发送，地址: %s,%d\n", client->server_ip, client->server_port);
                if (strcmp(type, "capture") == 0) {
                    publish_tcp_client_send(g_tcp_client, base64_encoded_data, type, temp_char);
                } else {
                    publish_tcp_client_send(g_tcp_client, base64_encoded_data, type, NULL);
                }
            } else {
                // MQTT模式 - 添加网络质量感知的分块处理
                bool network_good = check_network_quality(HOST);
                // 根据网络质量动态调整分块大小
                size_t once_size;
                if (network_good) {
                    once_size = 1024 * 64; // 网络良好时使用64KB
                    print_with_time("网络状态良好，使用较大分块: %zu KB\n", once_size/1024);
                } else {
                    once_size = 1024 * 16; // 网络较差时使用16KB
                    print_with_time("网络状态不佳，降低分块大小: %zu KB\n", once_size/1024);
                }
                
                char round[MAX_ROUND_STR];
                size_t total_chunks = (base64_size + once_size - 1) / once_size;
                char *once_data = malloc(once_size + 1);
                
                if (!once_data) {
                    print_error_with_time("内存分配失败\n");
                    free(base64_encoded_data);
                    return;
                }
                
                print_with_time("开始分块发送数据，共%zu块，总大小约%zu KB\n", 
                               total_chunks, base64_size/1024);
                               
                int consecutive_failures = 0;
                int max_consecutive_failures = 3;
                
                for (size_t i = 0; i < total_chunks; i++) {
                    // 检查连续失败次数
                    if (consecutive_failures >= max_consecutive_failures) {
                        print_error_with_time("连续发送失败%d次，中止发送\n", consecutive_failures);
                        break;
                    }
                    
                    size_t offset = i * once_size;
                    size_t current_size = (i == total_chunks - 1) ? (base64_size - offset) : once_size;
                    
                    memset(once_data, 0, once_size + 1);
                    memcpy(once_data, base64_encoded_data + offset, current_size);
                    once_data[current_size] = '\0';
                    
                    sprintf(round, "%zu/%zu", i + 1, total_chunks);
                    print_with_time("发送分段 %s, 大小: %zu\n", round, current_size);
                    
                    bool published = false;
                    
                    // 发送当前块
                    if (strcmp(type, "capture") == 0) {
                        publish_image_temp_base64(mosq, type, round, current_size, once_data, temp_char);
                    } else {
                        publish_image_base64(mosq, type, round, current_size, once_data);
                    }
                    
                    // 添加动态延迟，根据网络状况调整
                    if (network_good) {
                        if (total_chunks > 10 && i % 10 == 0 && i > 0) {
                            usleep(100000); // 网络好时每10块延迟100ms
                        }
                    } else {
                        if (i % 5 == 0 && i > 0) {
                            usleep(300000); // 网络差时每5块延迟300ms
                        } else {
                            usleep(100000); // 每块基本延迟100ms
                        }
                    }
                    
                    // 如果发送了一半以上，检查MQTT连接状态
                    if (i > total_chunks / 2 && i % 20 == 0) {
                        if (mosquitto_socket(mosq) == MOSQ_ERR_NO_CONN) {
                            print_error_with_time("检测到MQTT连接已断开，尝试重连\n");
                            int reconnect_result = mosquitto_reconnect(mosq);
                            if (reconnect_result != MOSQ_ERR_SUCCESS) {
                                print_error_with_time("重连失败: %s\n", mosquitto_strerror(reconnect_result));
                                consecutive_failures++;
                            }
                        }
                    }
                }
                
                // 发送完成后的资源释放
                free(once_data);
                free(base64_encoded_data);
                
                // 如果文件较大，发送完成后主动释放资源
                if (total_chunks > 50) {
                    print_with_time("大文件发送完成，执行主动内存清理\n");
                    mosquitto_loop(mosq, 100, 1); // 处理任何剩余消息
                }
            }
        }
        
        // 确保释放内存
        if(input_data) {
            // 现在将指针设为NULL，防止后续错误使用
            input_data = NULL;
        }

        // 在base64编码后添加大小检查
        if (base64_encoded_data && base64_size > BASE64_MAX_SIZE) {
            print_with_time("错误：图片太大，超过最大限制\n");
            free(base64_encoded_data);
            base64_encoded_data = NULL;
            input_data = "图片大小超过限制！";
        }

        // 更新文件后，重启系统
        // if(strcmp(input_data, "文件上传成功！")==0) 
        //     reboot(LINUX_REBOOT_CMD_RESTART);
    }else
        print_with_time("JSON data format error!\n");

    // 确保释放JSON对象
    if (json) {
        cJSON_Delete(json);
        json = NULL;
    }
    
    // 确保释放其他可能的内存
    if (!json_processed && input_data) {
        free(input_data);
        input_data = NULL;
    }
}

// 更新 MQTT 配置函数
void update_mqtt_config(void)
{ 
    // 从配置中读取并更新图像传输模式
    print_with_time("图像传输模式: %d\n", g_device_config.image_protocol);
    image_net_model = g_device_config.image_protocol;
    
    // 也可以在这里更新其他 MQTT 相关配置
    // 例如：服务器地址、端口等
} 