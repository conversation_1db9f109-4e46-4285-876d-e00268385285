UID          PID    PPID  C STIME TTY          TIME CMD
root           1       0  0 8月25 ?       00:01:02 /sbin/init splash
root           2       0  0 8月25 ?       00:00:00 [kthreadd]
root           3       2  0 8月25 ?       00:00:00 [rcu_gp]
root           4       2  0 8月25 ?       00:00:00 [rcu_par_gp]
root           5       2  0 8月25 ?       00:00:00 [slub_flushwq]
root           6       2  0 8月25 ?       00:00:00 [netns]
root           8       2  0 8月25 ?       00:00:00 [kworker/0:0H-events_highpri]
root          10       2  0 8月25 ?       00:00:00 [mm_percpu_wq]
root          11       2  0 8月25 ?       00:00:00 [rcu_tasks_kthread]
root          12       2  0 8月25 ?       00:00:00 [rcu_tasks_rude_kthread]
root          13       2  0 8月25 ?       00:00:00 [rcu_tasks_trace_kthread]
root          14       2  0 8月25 ?       00:00:08 [ksoftirqd/0]
root          15       2  0 8月25 ?       00:01:26 [rcu_preempt]
root          16       2  0 8月25 ?       00:00:00 [migration/0]
root          17       2  0 8月25 ?       00:00:00 [idle_inject/0]
root          19       2  0 8月25 ?       00:00:00 [cpuhp/0]
root          20       2  0 8月25 ?       00:00:00 [cpuhp/1]
root          21       2  0 8月25 ?       00:00:00 [idle_inject/1]
root          22       2  0 8月25 ?       00:00:20 [migration/1]
root          23       2  0 8月25 ?       00:00:01 [ksoftirqd/1]
root          24       2  0 8月25 ?       00:00:00 [kworker/1:0-events]
root          25       2  0 8月25 ?       00:00:00 [kworker/1:0H-events_highpri]
root          26       2  0 8月25 ?       00:00:00 [cpuhp/2]
root          27       2  0 8月25 ?       00:00:00 [idle_inject/2]
root          28       2  0 8月25 ?       00:00:00 [migration/2]
root          29       2  0 8月25 ?       00:00:04 [ksoftirqd/2]
root          31       2  0 8月25 ?       00:00:00 [kworker/2:0H-events_highpri]
root          32       2  0 8月25 ?       00:00:00 [cpuhp/3]
root          33       2  0 8月25 ?       00:00:00 [idle_inject/3]
root          34       2  0 8月25 ?       00:00:16 [migration/3]
root          35       2  0 8月25 ?       00:00:00 [ksoftirqd/3]
root          37       2  0 8月25 ?       00:00:00 [kworker/3:0H-events_highpri]
root          38       2  0 8月25 ?       00:00:00 [cpuhp/4]
root          39       2  0 8月25 ?       00:00:00 [idle_inject/4]
root          40       2  0 8月25 ?       00:00:00 [migration/4]
root          41       2  0 8月25 ?       00:00:04 [ksoftirqd/4]
root          43       2  0 8月25 ?       00:00:00 [kworker/4:0H-events_highpri]
root          44       2  0 8月25 ?       00:00:00 [cpuhp/5]
root          45       2  0 8月25 ?       00:00:00 [idle_inject/5]
root          46       2  0 8月25 ?       00:00:16 [migration/5]
root          47       2  0 8月25 ?       00:00:00 [ksoftirqd/5]
root          49       2  0 8月25 ?       00:00:00 [kworker/5:0H-events_highpri]
root          50       2  0 8月25 ?       00:00:00 [cpuhp/6]
root          51       2  0 8月25 ?       00:00:00 [idle_inject/6]
root          52       2  0 8月25 ?       00:00:00 [migration/6]
root          53       2  0 8月25 ?       00:00:04 [ksoftirqd/6]
root          55       2  0 8月25 ?       00:00:00 [kworker/6:0H-events_highpri]
root          56       2  0 8月25 ?       00:00:00 [cpuhp/7]
root          57       2  0 8月25 ?       00:00:00 [idle_inject/7]
root          58       2  0 8月25 ?       00:00:17 [migration/7]
root          59       2  0 8月25 ?       00:00:00 [ksoftirqd/7]
root          61       2  0 8月25 ?       00:00:00 [kworker/7:0H-events_highpri]
root          62       2  0 8月25 ?       00:00:00 [cpuhp/8]
root          63       2  0 8月25 ?       00:00:00 [idle_inject/8]
root          64       2  0 8月25 ?       00:00:16 [migration/8]
root          65       2  0 8月25 ?       00:00:00 [ksoftirqd/8]
root          67       2  0 8月25 ?       00:00:00 [kworker/8:0H-events_highpri]
root          68       2  0 8月25 ?       00:00:00 [cpuhp/9]
root          69       2  0 8月25 ?       00:00:00 [idle_inject/9]
root          70       2  0 8月25 ?       00:00:16 [migration/9]
root          71       2  0 8月25 ?       00:00:00 [ksoftirqd/9]
root          73       2  0 8月25 ?       00:00:00 [kworker/9:0H-events_highpri]
root          74       2  0 8月25 ?       00:00:00 [cpuhp/10]
root          75       2  0 8月25 ?       00:00:00 [idle_inject/10]
root          76       2  0 8月25 ?       00:00:21 [migration/10]
root          77       2  0 8月25 ?       00:00:00 [ksoftirqd/10]
root          79       2  0 8月25 ?       00:00:00 [kworker/10:0H-events_highpri]
root          80       2  0 8月25 ?       00:00:00 [cpuhp/11]
root          81       2  0 8月25 ?       00:00:00 [idle_inject/11]
root          82       2  0 8月25 ?       00:00:41 [migration/11]
root          83       2  0 8月25 ?       00:00:00 [ksoftirqd/11]
root          85       2  0 8月25 ?       00:00:00 [kworker/11:0H-events_highpri]
root          86       2  0 8月25 ?       00:00:00 [kdevtmpfs]
root          87       2  0 8月25 ?       00:00:00 [inet_frag_wq]
root          88       2  0 8月25 ?       00:00:00 [kauditd]
root          89       2  0 8月25 ?       00:00:00 [khungtaskd]
root          91       2  0 8月25 ?       00:00:00 [oom_reaper]
root          93       2  0 8月25 ?       00:00:00 [writeback]
root          94       2  0 8月25 ?       00:00:04 [kcompactd0]
root          95       2  0 8月25 ?       00:00:00 [ksmd]
root          97       2  0 8月25 ?       00:00:00 [khugepaged]
root          98       2  0 8月25 ?       00:00:00 [kintegrityd]
root          99       2  0 8月25 ?       00:00:00 [kblockd]
root         100       2  0 8月25 ?       00:00:00 [blkcg_punt_bio]
root         101       2  0 8月25 ?       00:00:00 [tpm_dev_wq]
root         102       2  0 8月25 ?       00:00:00 [ata_sff]
root         103       2  0 8月25 ?       00:00:00 [md]
root         104       2  0 8月25 ?       00:00:00 [edac-poller]
root         105       2  0 8月25 ?       00:00:00 [devfreq_wq]
root         106       2  0 8月25 ?       00:00:00 [watchdogd]
root         108       2  0 8月25 ?       00:00:00 [kworker/6:1H-kblockd]
root         109       2  0 8月25 ?       00:00:00 [kswapd0]
root         110       2  0 8月25 ?       00:00:00 [ecryptfs-kthread]
root         112       2  0 8月25 ?       00:00:00 [kthrotld]
root         113       2  0 8月25 ?       00:00:00 [irq/122-aerdrv]
root         114       2  0 8月25 ?       00:00:00 [irq/123-aerdrv]
root         115       2  0 8月25 ?       00:00:00 [acpi_thermal_pm]
root         116       2  0 8月25 ?       00:00:00 [hfi-updates]
root         117       2  0 8月25 ?       00:00:12 [kworker/2:1-mm_percpu_wq]
root         118       2  0 8月25 ?       00:00:00 [mld]
root         119       2  0 8月25 ?       00:00:00 [ipv6_addrconf]
root         120       2  0 8月25 ?       00:00:00 [kworker/2:1H-kblockd]
root         127       2  0 8月25 ?       00:00:00 [kstrp]
root         129       2  0 8月25 ?       00:00:03 [kworker/7:1-events]
root         131       2  0 8月25 ?       00:00:03 [kworker/10:1-mm_percpu_wq]
root         132       2  0 8月25 ?       00:00:02 [kworker/11:1-mm_percpu_wq]
root         133       2  0 8月25 ?       00:00:05 [kworker/1:1-mm_percpu_wq]
root         134       2  0 8月25 ?       00:00:06 [kworker/5:1-mm_percpu_wq]
root         136       2  0 8月25 ?       00:00:00 [zswap-shrink]
root         137       2  0 8月25 ?       00:00:02 [kworker/u25:0-i915_flip]
root         141       2  0 8月25 ?       00:00:00 [charger_manager]
root         167       2  0 8月25 ?       00:00:00 [kworker/0:1H-kblockd]
root         206       2  0 8月25 ?       00:00:00 [kworker/10:1H-kblockd]
root         207       2  0 8月25 ?       00:00:00 [kworker/8:1H-kblockd]
root         208       2  0 8月25 ?       00:00:00 [kworker/11:1H-kblockd]
root         209       2  0 8月25 ?       00:00:00 [kworker/1:1H-kblockd]
root         210       2  0 8月25 ?       00:00:00 [kworker/4:1H-kblockd]
root         211       2  0 8月25 ?       00:00:00 [kworker/5:1H-kblockd]
root         212       2  0 8月25 ?       00:00:00 [kworker/3:1H-kblockd]
root         213       2  0 8月25 ?       00:00:00 [kworker/9:1H-kblockd]
root         214       2  0 8月25 ?       00:00:00 [kworker/7:1H-kblockd]
root         220       2  0 8月25 ?       00:00:00 [scsi_eh_0]
root         221       2  0 8月25 ?       00:00:00 [scsi_tmf_0]
root         222       2  0 8月25 ?       00:00:00 [scsi_eh_1]
root         223       2  0 8月25 ?       00:00:00 [scsi_tmf_1]
root         237       2  0 8月25 ?       00:00:10 [kworker/4:2-mm_percpu_wq]
root         238       2  0 8月25 ?       00:00:00 [kworker/7:2-rcu_gp]
root         271       2  0 8月25 ?       00:00:04 [jbd2/sda2-8]
root         272       2  0 8月25 ?       00:00:00 [ext4-rsv-conver]
root         315       1  0 8月25 ?       00:00:05 /lib/systemd/systemd-journald
root         351       1  0 8月25 ?       00:00:00 /lib/systemd/systemd-udevd
root         404       2  0 8月25 ?       00:00:12 [kworker/0:3-events]
root         444       2  0 8月25 ?       00:00:00 [irq/128-mei_me]
root         472       2  0 8月25 ?       00:00:03 [kworker/9:2-mm_percpu_wq]
root         522       2  0 8月25 ?       00:00:00 [cryptd]
root         535       2  0 8月25 ?       00:00:00 [card0-crtc0]
root         536       2  0 8月25 ?       00:00:00 [card0-crtc1]
root         537       2  0 8月25 ?       00:00:00 [card0-crtc2]
root         538       2  0 8月25 ?       00:00:00 [card0-crtc3]
root         539       2  0 8月25 ?       00:00:05 [kworker/3:2-mm_percpu_wq]
systemd+     733       1  0 8月25 ?       00:02:12 /lib/systemd/systemd-oomd
systemd+     734       1  0 8月25 ?       00:00:03 /lib/systemd/systemd-resolved
systemd+     735       1  0 8月25 ?       00:00:00 /lib/systemd/systemd-timesyncd
avahi        781       1  0 8月25 ?       00:01:10 avahi-daemon: running [user-Default-string.local]
message+     783       1  0 8月25 ?       00:00:45 @dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
root         798       1  0 8月25 ?       00:00:06 /usr/sbin/irqbalance --foreground
root         847       1  0 8月25 ?       00:00:01 /usr/libexec/polkitd --no-debug
root         848       1  0 8月25 ?       00:00:00 /usr/libexec/power-profiles-daemon
root         850       1  0 8月25 ?       00:00:03 /usr/local/sunlogin/bin/oray_rundaemon -m server
root         856       1  0 8月25 ?       00:00:07 /usr/lib/snapd/snapd
root         862       1  0 8月25 ?       00:00:03 /usr/libexec/accounts-daemon
root         865       1  0 8月25 ?       00:00:01 /usr/sbin/cron -f -P
root         866       1  0 8月25 ?       00:00:00 /usr/libexec/switcheroo-control
root         869       1  0 8月25 ?       00:00:11 /lib/systemd/systemd-logind
root         870       1  0 8月25 ?       00:00:08 /usr/sbin/thermald --systemd --dbus-enable --adaptive
root         873       1  0 8月25 ?       00:00:05 /usr/libexec/udisks2/udisksd
avahi        880     781  0 8月25 ?       00:00:00 avahi-daemon: chroot helper
root         908       2  0 8月25 ?       00:00:37 [GEVThread0]
root         920       2  0 8月25 ?       00:00:38 [GEVThread0]
root         931       1  1 8月25 ?       00:19:50 /opt/MVS/logserver/MvLogServer
root         937       2  0 8月25 ?       00:00:37 [GEVThread0]
root         943       2  0 8月25 ?       00:00:37 [GEVThread0]
root         951     850  3 8月25 ?       00:47:01 /usr/local/sunlogin/bin/sunloginclient --mod=service
root         953       2  0 8月25 ?       00:00:35 [GEVThread0]
syslog      1008       1  0 8月25 ?       00:00:01 /usr/sbin/rsyslogd -n -iNONE
root        1014       1  0 8月25 ?       00:00:00 /sbin/wpa_supplicant -u -s -O DIR=/run/wpa_supplicant GROUP=netdev
root        1016       1  0 8月25 ?       00:00:00 /usr/sbin/ModemManager
root        1018       1  0 8月25 ?       00:01:16 /usr/sbin/NetworkManager --no-daemon
root        1036       1  0 8月25 ?       00:00:00 /usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal
root        1048       1  0 8月25 ?       00:00:00 /usr/sbin/gdm3
colord      1052       1  0 8月25 ?       00:00:00 /usr/libexec/colord
root        1068    1048  0 8月25 ?       00:00:00 gdm-session-worker [pam/gdm-autologin]
user        1083       1  0 8月25 ?       00:00:39 /lib/systemd/systemd --user
user        1084    1083  0 8月25 ?       00:00:00 (sd-pam)
user        1128    1083  0 8月25 ?       00:00:00 /usr/bin/pipewire
user        1132    1083  0 8月25 ?       00:00:00 /usr/bin/wireplumber
user        1133    1083  0 8月25 ?       00:00:00 /usr/bin/pipewire-pulse
user        1136    1083  0 8月25 ?       00:00:00 /usr/bin/gnome-keyring-daemon --foreground --components=pkcs11,secrets --control-directory=/run/user/1000/keyring
user        1146    1083  0 8月25 ?       00:00:02 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
rtkit       1154       1  0 8月25 ?       00:00:01 /usr/libexec/rtkit-daemon
user        1158    1068  0 8月25 tty2    00:00:00 /usr/libexec/gdm-x-session --run-script env GNOME_SHELL_SESSION_MODE=ubuntu /usr/bin/gnome-session --session=ubuntu
user        1160    1158  0 8月25 tty2    00:04:50 /usr/lib/xorg/Xorg vt2 -displayfd 3 -auth /run/user/1000/gdm/Xauthority -nolisten tcp -background none -noreset -keeptty -novtswitch -verbose 3
root        1223       2  0 8月25 ?       00:00:00 [kworker/5:2-events]
user        1257    1083  0 8月25 ?       00:00:00 /usr/libexec/xdg-document-portal
user        1261    1083  0 8月25 ?       00:00:00 /usr/libexec/xdg-permission-store
root        1268    1257  0 8月25 ?       00:00:00 fusermount3 -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/1000/doc
root        1274       2  0 8月25 ?       00:00:00 [kworker/11:2]
user        1307    1158  0 8月25 tty2    00:00:00 /usr/libexec/gnome-session-binary --session=ubuntu
user        1410    1083  0 8月25 ?       00:00:00 /usr/libexec/at-spi-bus-launcher
user        1418    1410  0 8月25 ?       00:00:00 /usr/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 11 --address=unix:path=/run/user/1000/at-spi/bus_0
user        1439    1083  0 8月25 ?       00:00:00 /usr/libexec/gcr-ssh-agent /run/user/1000/gcr
user        1440    1083  0 8月25 ?       00:00:00 /usr/libexec/gnome-session-ctl --monitor
user        1442    1083  0 8月25 ?       00:00:00 ssh-agent -D -a /run/user/1000/openssh_agent
user        1452    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfsd
user        1458    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfsd-fuse /run/user/1000/gvfs -f
user        1460    1083  0 8月25 ?       00:00:00 /usr/libexec/gnome-session-binary --systemd-service --session=ubuntu
user        1491    1083  0 8月25 ?       00:01:12 /usr/bin/gnome-shell
user        1539    1491  0 8月25 ?       00:00:01 /usr/libexec/mutter-x11-frames
user        1593    1083  0 8月25 ?       00:00:00 /usr/libexec/gnome-shell-calendar-server
user        1603    1083  0 8月25 ?       00:00:00 /usr/libexec/evolution-source-registry
root        1629       1  0 8月25 ?       00:00:00 /usr/libexec/upowerd
user        1640    1083  0 8月25 ?       00:00:00 /usr/libexec/dconf-service
user        1648    1083  0 8月25 ?       00:00:01 /usr/libexec/gvfs-udisks2-volume-monitor
user        1654    1083  0 8月25 ?       00:00:04 /usr/libexec/gvfs-afc-volume-monitor
user        1660    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfs-goa-volume-monitor
user        1665    1083  0 8月25 ?       00:00:00 /usr/libexec/goa-daemon
user        1674    1083  0 8月25 ?       00:00:30 /usr/libexec/goa-identity-service
user        1680    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfs-mtp-volume-monitor
user        1686    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfs-gphoto2-volume-monitor
user        1693    1083  0 8月25 ?       00:00:00 /usr/libexec/evolution-calendar-factory
user        1699    1083  0 8月25 ?       00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.Shell.Notifications
user        1721    1083  0 8月25 ?       00:00:00 /usr/libexec/evolution-addressbook-factory
user        1737    1452  0 8月25 ?       00:00:00 /usr/libexec/gvfsd-trash --spawner :1.23 /org/gtk/gvfs/exec_spaw/0
user        1748    1083  0 8月25 ?       00:00:00 /usr/libexec/at-spi2-registryd --use-gnome-session
user        1787    1083  0 8月25 ?       00:00:00 sh -c /usr/bin/ibus-daemon --panel disable $([ "$XDG_SESSION_TYPE" = "x11" ] && echo "--xim")
user        1788    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-a11y-settings
user        1790    1787  0 8月25 ?       00:00:05 /usr/bin/ibus-daemon --panel disable --xim
user        1791    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-color
user        1794    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-datetime
user        1796    1083  0 8月25 ?       00:00:02 /usr/libexec/gsd-housekeeping
user        1798    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-keyboard
user        1799    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-media-keys
user        1801    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-power
user        1803    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-print-notifications
user        1805    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-rfkill
user        1808    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-screensaver-proxy
user        1809    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-sharing
user        1811    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-smartcard
user        1812    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-sound
user        1813    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-wacom
user        1814    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-xsettings
user        1840    1460  0 8月25 ?       00:00:00 /usr/libexec/gsd-disk-utility-notify
user        1853    1083  0 8月25 ?       00:00:02 /usr/libexec/xdg-desktop-portal
user        1856    1460  8 8月25 ?       01:48:28 /opt/todesk/bin/ToDesk
user        1870    1460  3 8月25 ?       00:50:53 /usr/local/sunlogin/bin/sunloginclient --cmd=autorun
user        1875    1460  0 8月25 ?       00:00:00 /usr/libexec/evolution-data-server/evolution-alarm-notify
user        1919    1790  0 8月25 ?       00:00:00 /usr/libexec/ibus-memconf
user        1924    1790  0 8月25 ?       00:00:01 /usr/libexec/ibus-extension-gtk3
user        1932    1083  0 8月25 ?       00:00:00 /usr/libexec/ibus-x11 --kill-daemon
user        1937    1083  0 8月25 ?       00:00:00 /usr/libexec/ibus-portal
user        1943    1083  0 8月25 ?       00:00:00 /usr/libexec/xdg-desktop-portal-gnome
user        2100    1083  0 8月25 ?       00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.ScreenSaver
user        2103    1083  0 8月25 ?       00:00:00 /usr/libexec/gsd-printer
user        2122    1083  0 8月25 ?       00:00:02 /usr/libexec/tracker-miner-fs-3
user        2123    1870  0 8月25 ?       00:06:54 /usr/local/sunlogin/bin/sunloginclient --type=zygote --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36
user        2177    1790  0 8月25 ?       00:00:00 /usr/libexec/ibus-engine-libpinyin --ibus
user        2197    1870  0 8月25 ?       00:06:59 /proc/self/exe --type=gpu-process --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --supports-dual-gpus=false --gpu-driver-bug-workarounds=1,7,23,61,74 --disable-gl-extensions=GL_KHR_blend_equation_advanced GL_KHR_blend_equation_advanced_coherent --gpu-vendor-id=0x8086 --gpu-device-id=0x46a3 --gpu-driver-vendor --gpu-driver-version --gpu-driver-date --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --service-request-channel-token=ECD23083A0AF415C276760E8C259D66B
user        2202    1083  0 8月25 ?       00:00:00 /usr/libexec/xdg-desktop-portal-gtk
user        2368    1083  0 8月25 ?       00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
user        2510    2368  0 8月25 ?       00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
user        2579    1491  0 8月25 ?       00:00:39 gjs /usr/share/gnome-shell/extensions/<EMAIL>/ding.js -E -P /usr/share/gnome-shell/extensions/<EMAIL>
user        2599    1083  0 8月25 ?       00:00:00 /usr/libexec/gvfsd-metadata
root        2697       1  3 8月25 ?       00:47:57 /opt/todesk/bin/ToDesk_Service
kernoops    2700       1  0 8月25 ?       00:00:00 /usr/sbin/kerneloops --test
kernoops    2702       1  0 8月25 ?       00:00:00 /usr/sbin/kerneloops
user        3416    1460  0 8月25 ?       00:00:02 /usr/bin/update-notifier
root      153392       1  0 8月25 ?       00:00:00 sshd: /usr/sbin/sshd -D [listener] 0 of 10-100 startups
root      153872       2  0 8月25 ?       00:00:00 [tls-strp]
root      164395       2  0 8月25 ?       00:00:05 [kworker/8:0-mm_percpu_wq]
root     1014568       1  0 8月25 ?       00:00:00 /usr/sbin/cupsd -l
root     1014569       2  0 8月25 ?       00:00:05 [kworker/6:2-events]
root     1014570       2  0 8月25 ?       00:00:00 [kworker/3:0-events]
cups-br+ 1014571       1  0 8月25 ?       00:00:00 /usr/sbin/cups-browsed
lp       1014712 1014568  0 00:00 ?        00:00:00 /usr/lib/cups/notifier/dbus dbus://
lp       1014713 1014568  0 00:00 ?        00:00:00 /usr/lib/cups/notifier/dbus dbus://
root     1572987       2  0 05:21 ?        00:00:00 [kworker/0:0-events]
root     1812655       2  0 07:39 ?        00:00:00 [kworker/2:2-mm_percpu_wq]
root     2006690       2  0 09:30 ?        00:00:00 [kworker/6:0]
user     2038081       1 19 09:49 ?        00:24:56 /usr/local/sunlogin/bin/sunloginclient_desktop --mod=desktopagent --port=16067 --agentid=1
user     2038617    1083  0 09:49 ?        00:00:10 /usr/bin/nautilus --gapplication-service
user     2038669    1452  0 09:49 ?        00:00:00 /usr/libexec/gvfsd-recent --spawner :1.23 /org/gtk/gvfs/exec_spaw/1
user     2038933    1083  0 09:49 ?        00:00:24 /usr/libexec/gnome-terminal-server
root     2039706       2  0 09:49 ?        00:00:00 [kworker/9:1]
root     2039707       2  0 09:49 ?        00:00:00 [kworker/8:1]
user     2048395    1452  0 09:54 ?        00:00:00 /usr/libexec/gvfsd-network --spawner :1.23 /org/gtk/gvfs/exec_spaw/2
user     2048405    1452  0 09:54 ?        00:00:00 /usr/libexec/gvfsd-smb-browse --spawner :1.23 /org/gtk/gvfs/exec_spaw/3
user     2048413    1452  0 09:54 ?        00:00:00 /usr/libexec/gvfsd-dnssd --spawner :1.23 /org/gtk/gvfs/exec_spaw/4
user     2049471 2038933  0 09:55 pts/1    00:00:00 bash
root     2056456       2  0 09:59 ?        00:00:00 [kworker/u25:1]
root     2067395  153392  0 10:05 ?        00:00:13 sshd: root@notty
root     2067594       1  0 10:05 ?        00:00:00 /lib/systemd/systemd --user
root     2067595 2067594  0 10:05 ?        00:00:00 (sd-pam)
root     2067602 2067594  0 10:05 ?        00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
root     2067715 2067594  0 10:05 ?        00:00:00 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
root     2067716 2067594  0 10:05 ?        00:00:00 /usr/libexec/xdg-document-portal
root     2067717 2067395  0 10:05 ?        00:00:00 sh
root     2067728 2067594  0 10:05 ?        00:00:00 /usr/libexec/xdg-permission-store
root     2067744 2067716  0 10:05 ?        00:00:00 fusermount3 -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/0/doc
root     2067746 2067717  0 10:05 ?        00:00:20 /root/.vscode-server/code-848b80aeb52026648a8ff9f7c45a9b0a80641e2e command-shell --cli-data-dir /root/.vscode-server/cli --parent-process-id 2067717 --on-host=127.0.0.1 --on-port
root     2067819 2067602  0 10:05 ?        00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
root     2067824 2067746  0 10:05 ?        00:00:00 sh /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server --connection-token=remotessh --accept-server-license-terms --start-server --enable-remote-auto-shutdown --socket-path=/tmp/code-c899950c-9eae-42b4-9c0d-53e3ce72f2c5
root     2067828 2067824  0 10:05 ?        00:00:21 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js --connection-token=remotessh --accept-server-license-terms --start-server --enable-remote-auto-shutdown --socket-path=/tmp/code-c899950c-9eae-42b4-9c0d-53e3ce72f2c5
root     2067858 2067828  5 10:05 ?        00:06:02 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node --dns-result-order=ipv4first /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=extensionHost --transformURIs --useHostProxy=false
root     2067901 2067828  0 10:05 ?        00:00:14 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=fileWatcher
root     2067916 2067828  0 10:05 ?        00:00:13 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=ptyHost --logsPath /root/.vscode-server/data/logs/20250826T100542
root     2067972 2067858  0 10:05 ?        00:00:00 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/jsonServerMain --node-ipc --clientProcessId=2067858
root     2068749 2067916  0 10:06 pts/2    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2136453       2  0 10:44 ?        00:00:00 [kworker/4:1]
user     2157378 2049471  0 10:56 pts/1    00:00:01 ./mqttDemo 1112 ************ 6049 6050
root     2166433 2067916  0 11:01 pts/0    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2196190       2  0 11:18 ?        00:00:00 [kworker/u24:3-events_power_efficient]
root     2222550       2  0 11:33 ?        00:00:00 [kworker/10:0]
root     2232780       2  0 11:39 ?        00:00:00 [kworker/u24:1-ext4-rsv-conversion]
root     2234271 2067916  0 11:40 pts/3    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2249254       2  0 11:48 ?        00:00:00 [kworker/u24:0-events_power_efficient]
root     2258792       2  0 11:53 ?        00:00:00 [kworker/u24:2-events_unbound]
root     2263550 2067717  0 11:56 ?        00:00:00 sleep 180
root     2265805     865  0 11:58 ?        00:00:00 /usr/sbin/CRON -f -P
user     2265807 2265805  0 11:58 ?        00:00:00 /bin/sh -c sh /home/<USER>/dic/prog/runEverySeconds.sh
user     2265809 2265807  0 11:58 ?        00:00:00 sh /home/<USER>/dic/prog/runEverySeconds.sh
user     2267324 2265809  0 11:58 ?        00:00:00 sleep .967
root     2267326 2234271  0 11:58 pts/3    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2267335 2267326  0 11:58 pts/3    00:00:00 ps -ef
