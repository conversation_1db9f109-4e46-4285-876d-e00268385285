[     7.275] (--) Log file renamed from "/home/<USER>/.local/share/xorg/Xorg.pid-1120.log" to "/home/<USER>/.local/share/xorg/Xorg.0.log"
[     7.277] 
X.Org X Server ********
X Protocol Version 11, Revision 0
[     7.277] Current Operating System: Linux user-Default-string 6.2.0-39-generic #40-Ubuntu SMP PREEMPT_DYNAMIC Tue Nov 14 14:18:00 UTC 2023 x86_64
[     7.277] Kernel command line: BOOT_IMAGE=/boot/vmlinuz-6.2.0-39-generic root=UUID=141f9a10-ded6-4bb6-8e6b-34c8017a2dea ro quiet splash vt.handoff=7
[     7.277] xorg-server 2:21.1.7-1ubuntu3.6 (For technical support please see http://www.ubuntu.com/support) 
[     7.277] Current version of pixman: 0.42.2
[     7.277] 	Before reporting problems, check http://wiki.x.org
	to make sure that you have the latest version.
[     7.277] Markers: (--) probed, (**) from config file, (==) default setting,
	(++) from command line, (!!) notice, (II) informational,
	(WW) warning, (EE) error, (NI) not implemented, (??) unknown.
[     7.277] (==) Log file: "/home/<USER>/.local/share/xorg/Xorg.0.log", Time: Wed Aug 13 10:23:50 2025
[     7.278] (==) Using system config directory "/usr/share/X11/xorg.conf.d"
[     7.279] (==) No Layout section.  Using the first Screen section.
[     7.279] (==) No screen section available. Using defaults.
[     7.279] (**) |-->Screen "Default Screen Section" (0)
[     7.279] (**) |   |-->Monitor "<default monitor>"
[     7.279] (==) No monitor specified for screen "Default Screen Section".
	Using a default monitor configuration.
[     7.279] (==) Automatically adding devices
[     7.279] (==) Automatically enabling devices
[     7.279] (==) Automatically adding GPU devices
[     7.279] (==) Automatically binding GPU devices
[     7.279] (==) Max clients allowed: 256, resource mask: 0x1fffff
[     7.281] (WW) The directory "/usr/share/fonts/X11/cyrillic" does not exist.
[     7.281] 	Entry deleted from font path.
[     7.281] (WW) The directory "/usr/share/fonts/X11/100dpi/" does not exist.
[     7.281] 	Entry deleted from font path.
[     7.281] (WW) The directory "/usr/share/fonts/X11/75dpi/" does not exist.
[     7.281] 	Entry deleted from font path.
[     7.281] (WW) The directory "/usr/share/fonts/X11/100dpi" does not exist.
[     7.281] 	Entry deleted from font path.
[     7.281] (WW) The directory "/usr/share/fonts/X11/75dpi" does not exist.
[     7.281] 	Entry deleted from font path.
[     7.281] (==) FontPath set to:
	/usr/share/fonts/X11/misc,
	/usr/share/fonts/X11/Type1,
	built-ins
[     7.281] (==) ModulePath set to "/usr/lib/xorg/modules"
[     7.281] (II) The server relies on udev to provide the list of input devices.
	If no devices become available, reconfigure udev or disable AutoAddDevices.
[     7.281] (II) Loader magic: 0x5631579b8020
[     7.281] (II) Module ABI versions:
[     7.281] 	X.Org ANSI C Emulation: 0.4
[     7.281] 	X.Org Video Driver: 25.2
[     7.281] 	X.Org XInput driver : 24.4
[     7.281] 	X.Org Server Extension : 10.0
[     7.281] (++) using VT number 2

[     7.283] (II) systemd-logind: took control of session /org/freedesktop/login1/session/_31
[     7.283] (II) xfree86: Adding drm device (/dev/dri/card0)
[     7.283] (II) Platform probe for /sys/devices/pci0000:00/0000:00:02.0/drm/card0
[     7.284] (II) systemd-logind: got fd for /dev/dri/card0 226:0 fd 14 paused 0
[     7.285] (--) PCI:*(0@0:2:0) 8086:46a3:8086:2212 rev 12, Mem @ 0x6000000000/16777216, 0x4000000000/268435456, I/O @ 0x00005000/64, BIOS @ 0x????????/131072
[     7.285] (II) LoadModule: "glx"
[     7.286] (II) Loading /usr/lib/xorg/modules/extensions/libglx.so
[     7.299] (II) Module glx: vendor="X.Org Foundation"
[     7.299] 	compiled for ********, module version = 1.0.0
[     7.299] 	ABI class: X.Org Server Extension, version 10.0
[     7.299] (==) Matched modesetting as autoconfigured driver 0
[     7.299] (==) Matched fbdev as autoconfigured driver 1
[     7.299] (==) Matched vesa as autoconfigured driver 2
[     7.299] (==) Assigned the driver to the xf86ConfigLayout
[     7.299] (II) LoadModule: "modesetting"
[     7.299] (II) Loading /usr/lib/xorg/modules/drivers/modesetting_drv.so
[     7.306] (II) Module modesetting: vendor="X.Org Foundation"
[     7.306] 	compiled for ********, module version = 1.21.1
[     7.306] 	Module class: X.Org Video Driver
[     7.306] 	ABI class: X.Org Video Driver, version 25.2
[     7.306] (II) LoadModule: "fbdev"
[     7.306] (II) Loading /usr/lib/xorg/modules/drivers/fbdev_drv.so
[     7.308] (II) Module fbdev: vendor="X.Org Foundation"
[     7.308] 	compiled for ********, module version = 0.5.0
[     7.308] 	Module class: X.Org Video Driver
[     7.308] 	ABI class: X.Org Video Driver, version 25.2
[     7.308] (II) LoadModule: "vesa"
[     7.308] (II) Loading /usr/lib/xorg/modules/drivers/vesa_drv.so
[     7.309] (II) Module vesa: vendor="X.Org Foundation"
[     7.309] 	compiled for ********, module version = 2.5.0
[     7.309] 	Module class: X.Org Video Driver
[     7.309] 	ABI class: X.Org Video Driver, version 25.2
[     7.309] (II) modesetting: Driver for Modesetting Kernel Drivers: kms
[     7.309] (II) FBDEV: driver for framebuffer: fbdev
[     7.309] (II) VESA: driver for VESA chipsets: vesa
[     7.309] xf86EnableIO: failed to enable I/O ports 0000-03ff (Operation not permitted)
[     7.309] (II) modeset(0): using drv /dev/dri/card0
[     7.309] (WW) Falling back to old probe method for fbdev
[     7.309] (II) Loading sub module "fbdevhw"
[     7.309] (II) LoadModule: "fbdevhw"
[     7.309] (II) Loading /usr/lib/xorg/modules/libfbdevhw.so
[     7.311] (II) Module fbdevhw: vendor="X.Org Foundation"
[     7.311] 	compiled for ********, module version = 0.0.2
[     7.311] 	ABI class: X.Org Video Driver, version 25.2
[     7.311] (EE) open /dev/fb0: Permission denied
[     7.311] (WW) VGA arbiter: cannot open kernel arbiter, no multi-card support
[     7.311] (II) modeset(0): Creating default Display subsection in Screen section
	"Default Screen Section" for depth/fbbpp 24/32
[     7.311] (==) modeset(0): Depth 24, (==) framebuffer bpp 32
[     7.311] (==) modeset(0): RGB weight 888
[     7.311] (==) modeset(0): Default visual is TrueColor
[     7.311] (II) Loading sub module "glamoregl"
[     7.311] (II) LoadModule: "glamoregl"
[     7.311] (II) Loading /usr/lib/xorg/modules/libglamoregl.so
[     7.318] (II) Module glamoregl: vendor="X.Org Foundation"
[     7.318] 	compiled for ********, module version = 1.0.1
[     7.318] 	ABI class: X.Org ANSI C Emulation, version 0.4
[     7.761] (II) modeset(0): glamor X acceleration enabled on Mesa Intel(R) Graphics (ADL GT2)
[     7.761] (II) modeset(0): glamor initialized
[     7.761] (==) modeset(0): VariableRefresh: disabled
[     7.761] (==) modeset(0): AsyncFlipSecondaries: disabled
[     7.761] (II) modeset(0): Output HDMI-1 has no monitor section
[     7.761] (II) modeset(0): Output DP-1 has no monitor section
[     7.761] (II) modeset(0): Output HDMI-2 has no monitor section
[     7.788] (II) modeset(0): Output HDMI-3 has no monitor section
[     7.788] (II) modeset(0): Output DP-2 has no monitor section
[     7.788] (II) modeset(0): Output HDMI-4 has no monitor section
[     7.789] (II) modeset(0): EDID for output HDMI-1
[     7.789] (II) modeset(0): EDID for output DP-1
[     7.789] (II) modeset(0): EDID for output HDMI-2
[     7.817] (II) modeset(0): EDID for output HDMI-3
[     7.817] (II) modeset(0): Manufacturer: DEL  Model: 424a  Serial#: 1178097496
[     7.817] (II) modeset(0): Year: 2023  Week: 3
[     7.817] (II) modeset(0): EDID Version: 1.3
[     7.817] (II) modeset(0): Digital Display Input
[     7.817] (II) modeset(0): Max Image Size [cm]: horiz.: 53  vert.: 30
[     7.817] (II) modeset(0): Gamma: 2.20
[     7.817] (II) modeset(0): DPMS capabilities: StandBy Suspend Off
[     7.817] (II) modeset(0): Supported color encodings: RGB 4:4:4 YCrCb 4:4:4 
[     7.817] (II) modeset(0): First detailed timing is preferred mode
[     7.817] (II) modeset(0): redX: 0.657 redY: 0.330   greenX: 0.321 greenY: 0.624
[     7.817] (II) modeset(0): blueX: 0.154 blueY: 0.070   whiteX: 0.313 whiteY: 0.329
[     7.817] (II) modeset(0): Supported established timings:
[     7.817] (II) modeset(0): 720x400@70Hz
[     7.817] (II) modeset(0): 640x480@60Hz
[     7.817] (II) modeset(0): 640x480@75Hz
[     7.817] (II) modeset(0): 800x600@60Hz
[     7.817] (II) modeset(0): 800x600@75Hz
[     7.817] (II) modeset(0): 1024x768@60Hz
[     7.817] (II) modeset(0): 1024x768@75Hz
[     7.817] (II) modeset(0): 1280x1024@75Hz
[     7.817] (II) modeset(0): Manufacturer's mask: 0
[     7.817] (II) modeset(0): Supported standard timings:
[     7.817] (II) modeset(0): #0: hsize: 1152  vsize 864  refresh: 75  vid: 20337
[     7.817] (II) modeset(0): #1: hsize: 1280  vsize 1024  refresh: 60  vid: 32897
[     7.817] (II) modeset(0): #2: hsize: 1600  vsize 900  refresh: 60  vid: 49321
[     7.817] (II) modeset(0): #3: hsize: 1920  vsize 1080  refresh: 60  vid: 49361
[     7.817] (II) modeset(0): Supported detailed timing:
[     7.817] (II) modeset(0): clock: 148.5 MHz   Image Size:  527 x 296 mm
[     7.817] (II) modeset(0): h_active: 1920  h_sync: 2008  h_sync_end 2052 h_blank_end 2200 h_border: 0
[     7.817] (II) modeset(0): v_active: 1080  v_sync: 1084  v_sync_end 1089 v_blanking: 1125 v_border: 0
[     7.817] (II) modeset(0): Serial No: J2899V3
[     7.817] (II) modeset(0): Monitor name: DELL SE2422H
[     7.817] (II) modeset(0): Ranges: V min: 48 V max: 75 Hz, H min: 30 H max: 83 kHz, PixClock max 185 MHz
[     7.817] (II) modeset(0): Supported detailed timing:
[     7.817] (II) modeset(0): clock: 174.5 MHz   Image Size:  527 x 296 mm
[     7.817] (II) modeset(0): h_active: 1920  h_sync: 1968  h_sync_end 2000 h_blank_end 2080 h_border: 0
[     7.817] (II) modeset(0): v_active: 1080  v_sync: 1083  v_sync_end 1088 v_blanking: 1119 v_border: 0
[     7.817] (II) modeset(0): Supported detailed timing:
[     7.817] (II) modeset(0): clock: 74.2 MHz   Image Size:  527 x 296 mm
[     7.817] (II) modeset(0): h_active: 1920  h_sync: 2008  h_sync_end 2052 h_blank_end 2200 h_border: 0
[     7.817] (II) modeset(0): v_active: 540  v_sync: 542  v_sync_end 547 v_blanking: 562 v_border: 0
[     7.817] (II) modeset(0): Supported detailed timing:
[     7.817] (II) modeset(0): clock: 74.2 MHz   Image Size:  527 x 296 mm
[     7.817] (II) modeset(0): h_active: 1280  h_sync: 1390  h_sync_end 1430 h_blank_end 1650 h_border: 0
[     7.817] (II) modeset(0): v_active: 720  v_sync: 725  v_sync_end 730 v_blanking: 750 v_border: 0
[     7.817] (II) modeset(0): Supported detailed timing:
[     7.817] (II) modeset(0): clock: 27.0 MHz   Image Size:  527 x 296 mm
[     7.817] (II) modeset(0): h_active: 720  h_sync: 736  h_sync_end 798 h_blank_end 858 h_border: 0
[     7.817] (II) modeset(0): v_active: 480  v_sync: 489  v_sync_end 495 v_blanking: 525 v_border: 0
[     7.817] (II) modeset(0): Number of EDID sections to follow: 1
[     7.817] (II) modeset(0): EDID (in hex):
[     7.817] (II) modeset(0): 	00ffffffffffff0010ac4a4258573846
[     7.817] (II) modeset(0): 	0321010380351e78ea6785a854529f27
[     7.817] (II) modeset(0): 	125054a54b00714f8180a9c0d1c00101
[     7.817] (II) modeset(0): 	010101010101023a801871382d40582c
[     7.817] (II) modeset(0): 	45000f282100001e000000ff004a3238
[     7.817] (II) modeset(0): 	393956330a2020202020000000fc0044
[     7.817] (II) modeset(0): 	454c4c20534532343232480a000000fd
[     7.817] (II) modeset(0): 	00304b1e5312000a2020202020200193
[     7.817] (II) modeset(0): 	02031fb14b9005141f04131211030201
[     7.817] (II) modeset(0): 	65030c001000681a00000101304b002a
[     7.817] (II) modeset(0): 	4480a070382740302035000f28210000
[     7.817] (II) modeset(0): 	1a011d8018711c1620582c25000f2821
[     7.817] (II) modeset(0): 	00009e011d007251d01e206e2855000f
[     7.817] (II) modeset(0): 	282100001e8c0ad08a20e02d10103e96
[     7.817] (II) modeset(0): 	000f2821000018000000000000000000
[     7.817] (II) modeset(0): 	000000000000000000000000000000d8
[     7.817] (II) modeset(0): Printing probed modes for output HDMI-3
[     7.817] (II) modeset(0): Modeline "1920x1080"x60.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[     7.817] (II) modeset(0): Modeline "1920x1080"x75.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[     7.817] (II) modeset(0): Modeline "1920x1080"x50.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[     7.817] (II) modeset(0): Modeline "1920x1080"x59.9  148.35  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.4 kHz e)
[     7.817] (II) modeset(0): Modeline "1600x900"x60.0  108.00  1600 1624 1704 1800  900 901 904 1000 +hsync +vsync (60.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1280x1024"x75.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1280x1024"x60.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1152x864"x75.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[     7.817] (II) modeset(0): Modeline "1280x720"x60.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1280x720"x50.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[     7.817] (II) modeset(0): Modeline "1280x720"x59.9   74.18  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1024x768"x75.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[     7.817] (II) modeset(0): Modeline "1024x768"x60.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[     7.817] (II) modeset(0): Modeline "800x600"x75.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[     7.818] (II) modeset(0): Modeline "800x600"x60.3   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[     7.818] (II) modeset(0): Modeline "720x576"x50.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[     7.818] (II) modeset(0): Modeline "720x480"x60.0   27.03  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     7.818] (II) modeset(0): Modeline "720x480"x59.9   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     7.818] (II) modeset(0): Modeline "640x480"x75.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[     7.818] (II) modeset(0): Modeline "640x480"x60.0   25.20  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     7.818] (II) modeset(0): Modeline "640x480"x59.9   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     7.818] (II) modeset(0): Modeline "720x400"x70.1   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[     7.818] (II) modeset(0): EDID for output DP-2
[     7.818] (II) modeset(0): EDID for output HDMI-4
[     7.818] (II) modeset(0): Output HDMI-1 disconnected
[     7.818] (II) modeset(0): Output DP-1 disconnected
[     7.818] (II) modeset(0): Output HDMI-2 disconnected
[     7.818] (II) modeset(0): Output HDMI-3 connected
[     7.818] (II) modeset(0): Output DP-2 disconnected
[     7.818] (II) modeset(0): Output HDMI-4 disconnected
[     7.818] (II) modeset(0): Using exact sizes for initial modes
[     7.818] (II) modeset(0): Output HDMI-3 using initial mode 1920x1080 +0+0
[     7.818] (==) modeset(0): Using gamma correction (1.0, 1.0, 1.0)
[     7.818] (==) modeset(0): DPI set to (96, 96)
[     7.818] (II) Loading sub module "fb"
[     7.818] (II) LoadModule: "fb"
[     7.818] (II) Module "fb" already built-in
[     7.818] (II) UnloadModule: "fbdev"
[     7.818] (II) Unloading fbdev
[     7.818] (II) UnloadSubModule: "fbdevhw"
[     7.818] (II) Unloading fbdevhw
[     7.818] (II) UnloadModule: "vesa"
[     7.818] (II) Unloading vesa
[     7.861] (==) modeset(0): Backing store enabled
[     7.861] (==) modeset(0): Silken mouse enabled
[     8.011] (II) modeset(0): Initializing kms color map for depth 24, 8 bpc.
[     8.011] (==) modeset(0): DPMS enabled
[     8.011] (II) modeset(0): [DRI2] Setup complete
[     8.011] (II) modeset(0): [DRI2]   DRI driver: iris
[     8.011] (II) modeset(0): [DRI2]   VDPAU driver: va_gl
[     8.011] (II) Initializing extension Generic Event Extension
[     8.011] (II) Initializing extension SHAPE
[     8.011] (II) Initializing extension MIT-SHM
[     8.011] (II) Initializing extension XInputExtension
[     8.012] (II) Initializing extension XTEST
[     8.012] (II) Initializing extension BIG-REQUESTS
[     8.012] (II) Initializing extension SYNC
[     8.012] (II) Initializing extension XKEYBOARD
[     8.012] (II) Initializing extension XC-MISC
[     8.012] (II) Initializing extension SECURITY
[     8.012] (II) Initializing extension XFIXES
[     8.013] (II) Initializing extension RENDER
[     8.013] (II) Initializing extension RANDR
[     8.013] (II) Initializing extension COMPOSITE
[     8.013] (II) Initializing extension DAMAGE
[     8.013] (II) Initializing extension MIT-SCREEN-SAVER
[     8.013] (II) Initializing extension DOUBLE-BUFFER
[     8.013] (II) Initializing extension RECORD
[     8.013] (II) Initializing extension DPMS
[     8.013] (II) Initializing extension Present
[     8.014] (II) Initializing extension DRI3
[     8.014] (II) Initializing extension X-Resource
[     8.014] (II) Initializing extension XVideo
[     8.014] (II) Initializing extension XVideo-MotionCompensation
[     8.014] (II) Initializing extension SELinux
[     8.014] (II) SELinux: Disabled on system
[     8.014] (II) Initializing extension GLX
[     8.020] (II) AIGLX: Loaded and initialized iris
[     8.020] (II) GLX: Initialized DRI2 GL provider for screen 0
[     8.020] (II) Initializing extension XFree86-VidModeExtension
[     8.020] (II) Initializing extension XFree86-DGA
[     8.020] (II) Initializing extension XFree86-DRI
[     8.020] (II) Initializing extension DRI2
[     8.021] (II) modeset(0): Damage tracking initialized
[     8.021] (II) modeset(0): Setting screen physical size to 508 x 285
[     8.075] (II) config/udev: Adding input device Power Button (/dev/input/event2)
[     8.075] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.075] (II) LoadModule: "libinput"
[     8.075] (II) Loading /usr/lib/xorg/modules/input/libinput_drv.so
[     8.079] (II) Module libinput: vendor="X.Org Foundation"
[     8.079] 	compiled for 1.20.14, module version = 1.2.1
[     8.079] 	Module class: X.Org XInput Driver
[     8.079] 	ABI class: X.Org XInput driver, version 24.1
[     8.079] (II) Using input driver 'libinput' for 'Power Button'
[     8.079] (II) systemd-logind: got fd for /dev/input/event2 13:66 fd 28 paused 0
[     8.079] (**) Power Button: always reports core events
[     8.079] (**) Option "Device" "/dev/input/event2"
[     8.087] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.087] (II) event2  - Power Button: device is a keyboard
[     8.087] (II) event2  - Power Button: device removed
[     8.087] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXPWRBN:00/input/input2/event2"
[     8.087] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 6)
[     8.087] (**) Option "xkb_model" "pc105"
[     8.087] (**) Option "xkb_layout" "cn"
[     8.097] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.097] (II) event2  - Power Button: device is a keyboard
[     8.097] (II) config/udev: Adding input device Video Bus (/dev/input/event11)
[     8.097] (**) Video Bus: Applying InputClass "libinput keyboard catchall"
[     8.097] (II) Using input driver 'libinput' for 'Video Bus'
[     8.098] (II) systemd-logind: got fd for /dev/input/event11 13:75 fd 31 paused 0
[     8.098] (**) Video Bus: always reports core events
[     8.098] (**) Option "Device" "/dev/input/event11"
[     8.099] (II) event11 - Video Bus: is tagged by udev as: Keyboard
[     8.099] (II) event11 - Video Bus: device is a keyboard
[     8.099] (II) event11 - Video Bus: device removed
[     8.099] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0A08:00/LNXVIDEO:00/input/input12/event11"
[     8.099] (II) XINPUT: Adding extended input device "Video Bus" (type: KEYBOARD, id 7)
[     8.099] (**) Option "xkb_model" "pc105"
[     8.099] (**) Option "xkb_layout" "cn"
[     8.100] (II) event11 - Video Bus: is tagged by udev as: Keyboard
[     8.100] (II) event11 - Video Bus: device is a keyboard
[     8.100] (II) config/udev: Adding input device Power Button (/dev/input/event1)
[     8.100] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.100] (II) Using input driver 'libinput' for 'Power Button'
[     8.101] (II) systemd-logind: got fd for /dev/input/event1 13:65 fd 32 paused 0
[     8.101] (**) Power Button: always reports core events
[     8.101] (**) Option "Device" "/dev/input/event1"
[     8.101] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.101] (II) event1  - Power Button: device is a keyboard
[     8.101] (II) event1  - Power Button: device removed
[     8.101] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0C:00/input/input1/event1"
[     8.101] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 8)
[     8.101] (**) Option "xkb_model" "pc105"
[     8.101] (**) Option "xkb_layout" "cn"
[     8.102] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.102] (II) event1  - Power Button: device is a keyboard
[     8.102] (II) config/udev: Adding input device Sleep Button (/dev/input/event0)
[     8.102] (**) Sleep Button: Applying InputClass "libinput keyboard catchall"
[     8.102] (II) Using input driver 'libinput' for 'Sleep Button'
[     8.102] (II) systemd-logind: got fd for /dev/input/event0 13:64 fd 33 paused 0
[     8.102] (**) Sleep Button: always reports core events
[     8.102] (**) Option "Device" "/dev/input/event0"
[     8.103] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.103] (II) event0  - Sleep Button: device is a keyboard
[     8.103] (II) event0  - Sleep Button: device removed
[     8.103] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0E:00/input/input0/event0"
[     8.103] (II) XINPUT: Adding extended input device "Sleep Button" (type: KEYBOARD, id 9)
[     8.103] (**) Option "xkb_model" "pc105"
[     8.103] (**) Option "xkb_layout" "cn"
[     8.103] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.103] (II) event0  - Sleep Button: device is a keyboard
[     8.104] (II) config/udev: Adding input device Dell KB216 Wired Keyboard (/dev/input/event3)
[     8.104] (**) Dell KB216 Wired Keyboard: Applying InputClass "libinput keyboard catchall"
[     8.104] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard'
[     8.104] (II) systemd-logind: got fd for /dev/input/event3 13:67 fd 34 paused 0
[     8.104] (**) Dell KB216 Wired Keyboard: always reports core events
[     8.104] (**) Option "Device" "/dev/input/event3"
[     8.105] (II) event3  - Dell KB216 Wired Keyboard: is tagged by udev as: Keyboard
[     8.105] (II) event3  - Dell KB216 Wired Keyboard: device is a keyboard
[     8.105] (II) event3  - Dell KB216 Wired Keyboard: device removed
[     8.105] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.0/0003:413C:2113.0001/input/input3/event3"
[     8.105] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard" (type: KEYBOARD, id 10)
[     8.105] (**) Option "xkb_model" "pc105"
[     8.105] (**) Option "xkb_layout" "cn"
[     8.106] (II) event3  - Dell KB216 Wired Keyboard: is tagged by udev as: Keyboard
[     8.106] (II) event3  - Dell KB216 Wired Keyboard: device is a keyboard
[     8.106] (II) config/udev: Adding input device Dell KB216 Wired Keyboard Consumer Control (/dev/input/event4)
[     8.106] (**) Dell KB216 Wired Keyboard Consumer Control: Applying InputClass "libinput keyboard catchall"
[     8.106] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard Consumer Control'
[     8.107] (II) systemd-logind: got fd for /dev/input/event4 13:68 fd 35 paused 0
[     8.107] (**) Dell KB216 Wired Keyboard Consumer Control: always reports core events
[     8.107] (**) Option "Device" "/dev/input/event4"
[     8.108] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: is tagged by udev as: Keyboard
[     8.108] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device is a keyboard
[     8.108] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device removed
[     8.108] (II) libinput: Dell KB216 Wired Keyboard Consumer Control: needs a virtual subdevice
[     8.108] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input4/event4"
[     8.108] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard Consumer Control" (type: MOUSE, id 11)
[     8.108] (**) Option "AccelerationScheme" "none"
[     8.108] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) selected scheme none/0
[     8.108] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) acceleration factor: 2.000
[     8.108] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) acceleration threshold: 4
[     8.108] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: is tagged by udev as: Keyboard
[     8.108] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device is a keyboard
[     8.109] (II) config/udev: Adding input device Dell KB216 Wired Keyboard System Control (/dev/input/event5)
[     8.109] (**) Dell KB216 Wired Keyboard System Control: Applying InputClass "libinput keyboard catchall"
[     8.109] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard System Control'
[     8.109] (II) systemd-logind: got fd for /dev/input/event5 13:69 fd 36 paused 0
[     8.109] (**) Dell KB216 Wired Keyboard System Control: always reports core events
[     8.109] (**) Option "Device" "/dev/input/event5"
[     8.110] (II) event5  - Dell KB216 Wired Keyboard System Control: is tagged by udev as: Keyboard
[     8.110] (II) event5  - Dell KB216 Wired Keyboard System Control: device is a keyboard
[     8.110] (II) event5  - Dell KB216 Wired Keyboard System Control: device removed
[     8.110] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input5/event5"
[     8.110] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard System Control" (type: KEYBOARD, id 12)
[     8.110] (**) Option "xkb_model" "pc105"
[     8.110] (**) Option "xkb_layout" "cn"
[     8.111] (II) event5  - Dell KB216 Wired Keyboard System Control: is tagged by udev as: Keyboard
[     8.111] (II) event5  - Dell KB216 Wired Keyboard System Control: device is a keyboard
[     8.111] (II) config/udev: Adding input device ASUSTeK ROG STRIX IMPACT II WIRELESS (/dev/input/event6)
[     8.111] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS: Applying InputClass "libinput pointer catchall"
[     8.111] (II) Using input driver 'libinput' for 'ASUSTeK ROG STRIX IMPACT II WIRELESS'
[     8.168] (II) systemd-logind: got fd for /dev/input/event6 13:70 fd 37 paused 0
[     8.168] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS: always reports core events
[     8.168] (**) Option "Device" "/dev/input/event6"
[     8.168] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: is tagged by udev as: Mouse
[     8.169] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: device is a pointer
[     8.169] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: device removed
[     8.169] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.1/0003:0B05:1949.0004/input/input6/event6"
[     8.169] (II) XINPUT: Adding extended input device "ASUSTeK ROG STRIX IMPACT II WIRELESS" (type: MOUSE, id 13)
[     8.169] (**) Option "AccelerationScheme" "none"
[     8.169] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS: (accel) selected scheme none/0
[     8.169] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS: (accel) acceleration factor: 2.000
[     8.169] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS: (accel) acceleration threshold: 4
[     8.169] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: is tagged by udev as: Mouse
[     8.169] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: device is a pointer
[     8.170] (II) config/udev: Adding input device ASUSTeK ROG STRIX IMPACT II WIRELESS (/dev/input/mouse0)
[     8.170] (II) No input driver specified, ignoring this device.
[     8.170] (II) This device may have been added with another device file.
[     8.170] (II) config/udev: Adding input device ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard (/dev/input/event9)
[     8.170] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: Applying InputClass "libinput keyboard catchall"
[     8.170] (II) Using input driver 'libinput' for 'ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard'
[     8.170] (II) systemd-logind: got fd for /dev/input/event9 13:73 fd 38 paused 0
[     8.170] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: always reports core events
[     8.170] (**) Option "Device" "/dev/input/event9"
[     8.171] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: is tagged by udev as: Keyboard
[     8.171] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: device is a keyboard
[     8.171] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: device removed
[     8.171] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.2/0003:0B05:1949.0005/input/input10/event9"
[     8.171] (II) XINPUT: Adding extended input device "ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard" (type: KEYBOARD, id 14)
[     8.171] (**) Option "xkb_model" "pc105"
[     8.171] (**) Option "xkb_layout" "cn"
[     8.172] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: is tagged by udev as: Keyboard
[     8.172] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: device is a keyboard
[     8.172] (II) config/udev: Adding input device ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control (/dev/input/event7)
[     8.172] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: Applying InputClass "libinput keyboard catchall"
[     8.172] (II) Using input driver 'libinput' for 'ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control'
[     8.173] (II) systemd-logind: got fd for /dev/input/event7 13:71 fd 39 paused 0
[     8.173] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: always reports core events
[     8.173] (**) Option "Device" "/dev/input/event7"
[     8.174] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: is tagged by udev as: Keyboard
[     8.174] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: device is a keyboard
[     8.174] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: device removed
[     8.174] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.2/0003:0B05:1949.0005/input/input7/event7"
[     8.174] (II) XINPUT: Adding extended input device "ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control" (type: KEYBOARD, id 15)
[     8.174] (**) Option "xkb_model" "pc105"
[     8.174] (**) Option "xkb_layout" "cn"
[     8.175] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: is tagged by udev as: Keyboard
[     8.175] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: device is a keyboard
[     8.175] (II) config/udev: Adding input device ASUSTeK ROG STRIX IMPACT II WIRELESS System Control (/dev/input/event8)
[     8.175] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: Applying InputClass "libinput keyboard catchall"
[     8.175] (II) Using input driver 'libinput' for 'ASUSTeK ROG STRIX IMPACT II WIRELESS System Control'
[     8.176] (II) systemd-logind: got fd for /dev/input/event8 13:72 fd 40 paused 0
[     8.176] (**) ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: always reports core events
[     8.176] (**) Option "Device" "/dev/input/event8"
[     8.177] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: is tagged by udev as: Keyboard
[     8.177] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: device is a keyboard
[     8.177] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: device removed
[     8.177] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.2/0003:0B05:1949.0005/input/input8/event8"
[     8.177] (II) XINPUT: Adding extended input device "ASUSTeK ROG STRIX IMPACT II WIRELESS System Control" (type: KEYBOARD, id 16)
[     8.177] (**) Option "xkb_model" "pc105"
[     8.177] (**) Option "xkb_layout" "cn"
[     8.178] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: is tagged by udev as: Keyboard
[     8.178] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: device is a keyboard
[     8.178] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=3 (/dev/input/event12)
[     8.178] (II) No input driver specified, ignoring this device.
[     8.178] (II) This device may have been added with another device file.
[     8.178] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=7 (/dev/input/event13)
[     8.178] (II) No input driver specified, ignoring this device.
[     8.178] (II) This device may have been added with another device file.
[     8.179] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=8 (/dev/input/event14)
[     8.179] (II) No input driver specified, ignoring this device.
[     8.179] (II) This device may have been added with another device file.
[     8.179] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=9 (/dev/input/event15)
[     8.179] (II) No input driver specified, ignoring this device.
[     8.179] (II) This device may have been added with another device file.
[     8.179] (II) config/udev: Adding input device Intel HID events (/dev/input/event10)
[     8.179] (**) Intel HID events: Applying InputClass "libinput keyboard catchall"
[     8.179] (II) Using input driver 'libinput' for 'Intel HID events'
[     8.179] (II) systemd-logind: got fd for /dev/input/event10 13:74 fd 41 paused 0
[     8.179] (**) Intel HID events: always reports core events
[     8.179] (**) Option "Device" "/dev/input/event10"
[     8.180] (II) event10 - Intel HID events: is tagged by udev as: Keyboard
[     8.180] (II) event10 - Intel HID events: device is a keyboard
[     8.180] (II) event10 - Intel HID events: device removed
[     8.180] (**) Option "config_info" "udev:/sys/devices/platform/INTC1070:00/input/input11/event10"
[     8.180] (II) XINPUT: Adding extended input device "Intel HID events" (type: KEYBOARD, id 17)
[     8.180] (**) Option "xkb_model" "pc105"
[     8.180] (**) Option "xkb_layout" "cn"
[     8.180] (II) event10 - Intel HID events: is tagged by udev as: Keyboard
[     8.181] (II) event10 - Intel HID events: device is a keyboard
[     8.194] (**) Dell KB216 Wired Keyboard Consumer Control: Applying InputClass "libinput keyboard catchall"
[     8.194] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard Consumer Control'
[     8.194] (II) systemd-logind: returning pre-existing fd for /dev/input/event4 13:68
[     8.194] (**) Dell KB216 Wired Keyboard Consumer Control: always reports core events
[     8.194] (**) Option "Device" "/dev/input/event4"
[     8.194] (II) libinput: Dell KB216 Wired Keyboard Consumer Control: is a virtual subdevice
[     8.194] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input4/event4"
[     8.194] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard Consumer Control" (type: KEYBOARD, id 18)
[     8.194] (**) Option "xkb_model" "pc105"
[     8.194] (**) Option "xkb_layout" "cn"
[     8.961] (II) modeset(0): EDID vendor "DEL", prod id 16970
[     8.961] (II) modeset(0): Using EDID range info for horizontal sync
[     8.961] (II) modeset(0): Using EDID range info for vertical refresh
[     8.961] (II) modeset(0): Printing DDC gathered Modelines:
[     8.961] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[     8.961] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[     8.961] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[     8.961] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     8.961] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     8.961] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[     8.961] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[     8.961] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[     8.961] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[     8.961] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     8.961] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[     8.961] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[     8.961] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[     8.961] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[     8.961] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[     8.961] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[     8.961] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[     8.961] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[     8.961] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[     8.961] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[     9.994] (II) modeset(0): EDID vendor "DEL", prod id 16970
[     9.994] (II) modeset(0): Using hsync ranges from config file
[     9.994] (II) modeset(0): Using vrefresh ranges from config file
[     9.994] (II) modeset(0): Printing DDC gathered Modelines:
[     9.994] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[     9.994] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[     9.994] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[     9.994] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     9.994] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     9.994] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[     9.994] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[     9.994] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[     9.994] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[     9.994] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     9.994] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[     9.994] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[     9.994] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[     9.994] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[     9.994] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[     9.994] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[     9.994] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[     9.994] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[     9.994] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[     9.994] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[ 18854.561] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: SYN_DROPPED event - some input events have been lost.
[ 18854.594] (II) modeset(0): EDID vendor "DEL", prod id 16970
[ 18854.594] (II) modeset(0): Using hsync ranges from config file
[ 18854.594] (II) modeset(0): Using vrefresh ranges from config file
[ 18854.594] (II) modeset(0): Printing DDC gathered Modelines:
[ 18854.594] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[ 18854.594] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[ 18854.594] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[ 18854.594] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[ 18854.594] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[ 18854.594] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[ 18854.594] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[ 18854.661] (II) modeset(0): EDID vendor "DEL", prod id 16970
[ 18854.661] (II) modeset(0): Using hsync ranges from config file
[ 18854.661] (II) modeset(0): Using vrefresh ranges from config file
[ 18854.661] (II) modeset(0): Printing DDC gathered Modelines:
[ 18854.661] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[ 18854.661] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[ 18854.661] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[ 18854.661] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[ 18854.661] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[ 18854.661] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[ 18854.661] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[ 18876.662] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: SYN_DROPPED event - some input events have been lost.
[ 18876.695] (II) modeset(0): EDID vendor "DEL", prod id 16970
[ 18876.696] (II) modeset(0): Using hsync ranges from config file
[ 18876.696] (II) modeset(0): Using vrefresh ranges from config file
[ 18876.696] (II) modeset(0): Printing DDC gathered Modelines:
[ 18876.696] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[ 18876.696] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[ 18876.696] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[ 18876.696] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[ 18876.696] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[ 18876.696] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[ 18876.696] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[ 18876.762] (II) modeset(0): EDID vendor "DEL", prod id 16970
[ 18876.762] (II) modeset(0): Using hsync ranges from config file
[ 18876.762] (II) modeset(0): Using vrefresh ranges from config file
[ 18876.762] (II) modeset(0): Printing DDC gathered Modelines:
[ 18876.762] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[ 18876.762] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[ 18876.762] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[ 18876.762] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[ 18876.762] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[ 18876.762] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[ 18876.762] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[113000.688] (II) event6  - ASUSTeK ROG STRIX IMPACT II WIRELESS: device removed
[113000.689] (II) config/udev: removing device ASUSTeK ROG STRIX IMPACT II WIRELESS
[113000.689] (**) Option "fd" "37"
[113000.692] (II) UnloadModule: "libinput"
[113000.692] (II) systemd-logind: releasing fd for 13:70
[113000.768] (II) event7  - ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control: device removed
[113000.770] (II) config/udev: removing device ASUSTeK ROG STRIX IMPACT II WIRELESS Consumer Control
[113000.770] (**) Option "fd" "39"
[113000.771] (II) UnloadModule: "libinput"
[113000.771] (II) systemd-logind: releasing fd for 13:71
[113000.836] (II) event8  - ASUSTeK ROG STRIX IMPACT II WIRELESS System Control: device removed
[113000.837] (II) config/udev: removing device ASUSTeK ROG STRIX IMPACT II WIRELESS System Control
[113000.837] (**) Option "fd" "40"
[113000.839] (II) UnloadModule: "libinput"
[113000.839] (II) systemd-logind: releasing fd for 13:72
[113000.908] (II) event9  - ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard: device removed
[113000.909] (II) config/udev: removing device ASUSTeK ROG STRIX IMPACT II WIRELESS Keyboard
[113000.909] (**) Option "fd" "38"
[113000.911] (II) UnloadModule: "libinput"
[113000.911] (II) systemd-logind: releasing fd for 13:73
[115018.057] (II) modeset(0): EDID vendor "DEL", prod id 16970
[115018.057] (II) modeset(0): Using hsync ranges from config file
[115018.057] (II) modeset(0): Using vrefresh ranges from config file
[115018.057] (II) modeset(0): Printing DDC gathered Modelines:
[115018.057] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[115018.057] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[115018.057] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[115018.057] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[115018.057] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[115018.057] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[115018.057] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[115018.057] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[115018.057] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[115018.057] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[115018.057] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[115018.057] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[115018.057] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[115018.057] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[115018.057] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[115018.057] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[115018.057] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[115018.057] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[115018.057] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[115018.057] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[1038423.026] (II) modeset(0): EDID vendor "DEL", prod id 16970
[1038423.027] (II) modeset(0): Using hsync ranges from config file
[1038423.027] (II) modeset(0): Using vrefresh ranges from config file
[1038423.027] (II) modeset(0): Printing DDC gathered Modelines:
[1038423.027] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[1038423.027] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[1038423.027] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[1038423.027] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[1038423.027] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[1038423.027] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[1038423.027] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[1038423.027] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[1038423.027] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[1038423.027] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[1038423.027] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[1038423.027] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[1038423.027] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[1038423.027] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[1038423.027] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[1038423.195] (II) modeset(0): EDID vendor "DEL", prod id 16970
[1038423.195] (II) modeset(0): Using hsync ranges from config file
[1038423.195] (II) modeset(0): Using vrefresh ranges from config file
[1038423.195] (II) modeset(0): Printing DDC gathered Modelines:
[1038423.195] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[1038423.195] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[1038423.195] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[1038423.195] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[1038423.195] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[1038423.195] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[1038423.195] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[1038423.195] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[1038423.196] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[1038423.196] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[1038423.196] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[1038423.196] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[1038423.196] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[1038423.196] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[1038423.196] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[1038423.196] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[1038423.196] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[1038423.196] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[1038423.196] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[1038423.196] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[1038479.708] (II) modeset(0): EDID vendor "DEL", prod id 16970
[1038479.709] (II) modeset(0): Using hsync ranges from config file
[1038479.709] (II) modeset(0): Using vrefresh ranges from config file
[1038479.709] (II) modeset(0): Printing DDC gathered Modelines:
[1038479.709] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[1038479.709] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[1038479.709] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[1038479.709] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[1038479.709] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[1038479.709] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[1038479.709] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[1038479.709] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[1038479.709] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[1038479.709] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[1038479.709] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[1038479.709] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[1038479.709] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[1038479.709] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[1038479.709] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[1038479.835] (II) modeset(0): EDID vendor "DEL", prod id 16970
[1038479.835] (II) modeset(0): Using hsync ranges from config file
[1038479.835] (II) modeset(0): Using vrefresh ranges from config file
[1038479.835] (II) modeset(0): Printing DDC gathered Modelines:
[1038479.835] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[1038479.836] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[1038479.836] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[1038479.836] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[1038479.836] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[1038479.836] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[1038479.836] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[1038479.836] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[1038479.836] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[1038479.836] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[1038479.836] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[1038479.836] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[1038479.836] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[1038479.836] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[1038479.836] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[1038551.141] (II) config/udev: Adding input device PixArt Dell MS116 USB Optical Mouse (/dev/input/mouse0)
[1038551.143] (II) No input driver specified, ignoring this device.
[1038551.143] (II) This device may have been added with another device file.
[1038551.214] (II) config/udev: Adding input device PixArt Dell MS116 USB Optical Mouse (/dev/input/event6)
[1038551.214] (**) PixArt Dell MS116 USB Optical Mouse: Applying InputClass "libinput pointer catchall"
[1038551.215] (II) Using input driver 'libinput' for 'PixArt Dell MS116 USB Optical Mouse'
[1038551.216] (II) systemd-logind: got fd for /dev/input/event6 13:70 fd 83 paused 0
[1038551.217] (**) PixArt Dell MS116 USB Optical Mouse: always reports core events
[1038551.217] (**) Option "Device" "/dev/input/event6"
[1038551.219] (II) event6  - PixArt Dell MS116 USB Optical Mouse: is tagged by udev as: Mouse
[1038551.219] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device set to 1000 DPI
[1038551.220] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device is a pointer
[1038551.220] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device removed
[1038551.220] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.0/0003:413C:301A.0006/input/input17/event6"
[1038551.220] (II) XINPUT: Adding extended input device "PixArt Dell MS116 USB Optical Mouse" (type: MOUSE, id 13)
[1038551.221] (**) Option "AccelerationScheme" "none"
[1038551.222] (**) PixArt Dell MS116 USB Optical Mouse: (accel) selected scheme none/0
[1038551.222] (**) PixArt Dell MS116 USB Optical Mouse: (accel) acceleration factor: 2.000
[1038551.222] (**) PixArt Dell MS116 USB Optical Mouse: (accel) acceleration threshold: 4
[1038551.223] (II) event6  - PixArt Dell MS116 USB Optical Mouse: is tagged by udev as: Mouse
[1038551.223] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device set to 1000 DPI
[1038551.223] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device is a pointer
[1041061.998] (II) modeset(0): EDID vendor "DEL", prod id 16970
[1041061.998] (II) modeset(0): Using hsync ranges from config file
[1041061.998] (II) modeset(0): Using vrefresh ranges from config file
[1041061.998] (II) modeset(0): Printing DDC gathered Modelines:
[1041061.998] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[1041061.998] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[1041061.998] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[1041061.998] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[1041061.998] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[1041061.998] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[1041061.998] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[1041061.998] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[1041061.998] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[1041061.998] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[1041061.998] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[1041061.998] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[1041061.998] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[1041061.998] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[1041061.998] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
