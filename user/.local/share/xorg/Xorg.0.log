[     7.573] (--) Log file renamed from "/home/<USER>/.local/share/xorg/Xorg.pid-1160.log" to "/home/<USER>/.local/share/xorg/Xorg.0.log"
[     7.574] 
X.Org X Server ********
X Protocol Version 11, Revision 0
[     7.574] Current Operating System: Linux user-Default-string 6.2.0-39-generic #40-Ubuntu SMP PREEMPT_DYNAMIC Tue Nov 14 14:18:00 UTC 2023 x86_64
[     7.574] Kernel command line: BOOT_IMAGE=/boot/vmlinuz-6.2.0-39-generic root=UUID=141f9a10-ded6-4bb6-8e6b-34c8017a2dea ro quiet splash vt.handoff=7
[     7.574] xorg-server 2:21.1.7-1ubuntu3.6 (For technical support please see http://www.ubuntu.com/support) 
[     7.574] Current version of pixman: 0.42.2
[     7.574] 	Before reporting problems, check http://wiki.x.org
	to make sure that you have the latest version.
[     7.574] Markers: (--) probed, (**) from config file, (==) default setting,
	(++) from command line, (!!) notice, (II) informational,
	(WW) warning, (EE) error, (NI) not implemented, (??) unknown.
[     7.574] (==) Log file: "/home/<USER>/.local/share/xorg/Xorg.0.log", Time: Mon Aug 25 14:17:19 2025
[     7.575] (==) Using system config directory "/usr/share/X11/xorg.conf.d"
[     7.577] (==) No Layout section.  Using the first Screen section.
[     7.577] (==) No screen section available. Using defaults.
[     7.577] (**) |-->Screen "Default Screen Section" (0)
[     7.577] (**) |   |-->Monitor "<default monitor>"
[     7.577] (==) No monitor specified for screen "Default Screen Section".
	Using a default monitor configuration.
[     7.577] (==) Automatically adding devices
[     7.577] (==) Automatically enabling devices
[     7.577] (==) Automatically adding GPU devices
[     7.577] (==) Automatically binding GPU devices
[     7.577] (==) Max clients allowed: 256, resource mask: 0x1fffff
[     7.580] (WW) The directory "/usr/share/fonts/X11/cyrillic" does not exist.
[     7.580] 	Entry deleted from font path.
[     7.580] (WW) The directory "/usr/share/fonts/X11/100dpi/" does not exist.
[     7.580] 	Entry deleted from font path.
[     7.580] (WW) The directory "/usr/share/fonts/X11/75dpi/" does not exist.
[     7.580] 	Entry deleted from font path.
[     7.580] (WW) The directory "/usr/share/fonts/X11/100dpi" does not exist.
[     7.580] 	Entry deleted from font path.
[     7.580] (WW) The directory "/usr/share/fonts/X11/75dpi" does not exist.
[     7.580] 	Entry deleted from font path.
[     7.580] (==) FontPath set to:
	/usr/share/fonts/X11/misc,
	/usr/share/fonts/X11/Type1,
	built-ins
[     7.580] (==) ModulePath set to "/usr/lib/xorg/modules"
[     7.580] (II) The server relies on udev to provide the list of input devices.
	If no devices become available, reconfigure udev or disable AutoAddDevices.
[     7.580] (II) Loader magic: 0x5594bcc5e020
[     7.580] (II) Module ABI versions:
[     7.580] 	X.Org ANSI C Emulation: 0.4
[     7.580] 	X.Org Video Driver: 25.2
[     7.580] 	X.Org XInput driver : 24.4
[     7.580] 	X.Org Server Extension : 10.0
[     7.580] (++) using VT number 2

[     7.582] (II) systemd-logind: took control of session /org/freedesktop/login1/session/_31
[     7.583] (II) xfree86: Adding drm device (/dev/dri/card0)
[     7.583] (II) Platform probe for /sys/devices/pci0000:00/0000:00:02.0/drm/card0
[     7.583] (II) systemd-logind: got fd for /dev/dri/card0 226:0 fd 14 paused 0
[     7.584] (--) PCI:*(0@0:2:0) 8086:46a3:8086:2212 rev 12, Mem @ 0x6000000000/16777216, 0x4000000000/268435456, I/O @ 0x00005000/64, BIOS @ 0x????????/131072
[     7.584] (II) LoadModule: "glx"
[     7.589] (II) Loading /usr/lib/xorg/modules/extensions/libglx.so
[     7.607] (II) Module glx: vendor="X.Org Foundation"
[     7.607] 	compiled for ********, module version = 1.0.0
[     7.607] 	ABI class: X.Org Server Extension, version 10.0
[     7.607] (==) Matched modesetting as autoconfigured driver 0
[     7.607] (==) Matched fbdev as autoconfigured driver 1
[     7.607] (==) Matched vesa as autoconfigured driver 2
[     7.607] (==) Assigned the driver to the xf86ConfigLayout
[     7.607] (II) LoadModule: "modesetting"
[     7.607] (II) Loading /usr/lib/xorg/modules/drivers/modesetting_drv.so
[     7.609] (II) Module modesetting: vendor="X.Org Foundation"
[     7.609] 	compiled for ********, module version = 1.21.1
[     7.609] 	Module class: X.Org Video Driver
[     7.609] 	ABI class: X.Org Video Driver, version 25.2
[     7.609] (II) LoadModule: "fbdev"
[     7.609] (II) Loading /usr/lib/xorg/modules/drivers/fbdev_drv.so
[     7.610] (II) Module fbdev: vendor="X.Org Foundation"
[     7.610] 	compiled for ********, module version = 0.5.0
[     7.610] 	Module class: X.Org Video Driver
[     7.610] 	ABI class: X.Org Video Driver, version 25.2
[     7.610] (II) LoadModule: "vesa"
[     7.610] (II) Loading /usr/lib/xorg/modules/drivers/vesa_drv.so
[     7.610] (II) Module vesa: vendor="X.Org Foundation"
[     7.610] 	compiled for ********, module version = 2.5.0
[     7.610] 	Module class: X.Org Video Driver
[     7.610] 	ABI class: X.Org Video Driver, version 25.2
[     7.610] (II) modesetting: Driver for Modesetting Kernel Drivers: kms
[     7.610] (II) FBDEV: driver for framebuffer: fbdev
[     7.610] (II) VESA: driver for VESA chipsets: vesa
[     7.610] xf86EnableIO: failed to enable I/O ports 0000-03ff (Operation not permitted)
[     7.611] (II) modeset(0): using drv /dev/dri/card0
[     7.611] (WW) Falling back to old probe method for fbdev
[     7.611] (II) Loading sub module "fbdevhw"
[     7.611] (II) LoadModule: "fbdevhw"
[     7.611] (II) Loading /usr/lib/xorg/modules/libfbdevhw.so
[     7.611] (II) Module fbdevhw: vendor="X.Org Foundation"
[     7.611] 	compiled for ********, module version = 0.0.2
[     7.611] 	ABI class: X.Org Video Driver, version 25.2
[     7.611] (EE) open /dev/fb0: Permission denied
[     7.611] (WW) VGA arbiter: cannot open kernel arbiter, no multi-card support
[     7.611] (II) modeset(0): Creating default Display subsection in Screen section
	"Default Screen Section" for depth/fbbpp 24/32
[     7.611] (==) modeset(0): Depth 24, (==) framebuffer bpp 32
[     7.611] (==) modeset(0): RGB weight 888
[     7.611] (==) modeset(0): Default visual is TrueColor
[     7.611] (II) Loading sub module "glamoregl"
[     7.611] (II) LoadModule: "glamoregl"
[     7.611] (II) Loading /usr/lib/xorg/modules/libglamoregl.so
[     7.621] (II) Module glamoregl: vendor="X.Org Foundation"
[     7.621] 	compiled for ********, module version = 1.0.1
[     7.621] 	ABI class: X.Org ANSI C Emulation, version 0.4
[     8.045] (II) modeset(0): glamor X acceleration enabled on Mesa Intel(R) Graphics (ADL GT2)
[     8.045] (II) modeset(0): glamor initialized
[     8.045] (==) modeset(0): VariableRefresh: disabled
[     8.045] (==) modeset(0): AsyncFlipSecondaries: disabled
[     8.045] (II) modeset(0): Output HDMI-1 has no monitor section
[     8.045] (II) modeset(0): Output DP-1 has no monitor section
[     8.045] (II) modeset(0): Output HDMI-2 has no monitor section
[     8.073] (II) modeset(0): Output HDMI-3 has no monitor section
[     8.073] (II) modeset(0): Output DP-2 has no monitor section
[     8.073] (II) modeset(0): Output HDMI-4 has no monitor section
[     8.074] (II) modeset(0): EDID for output HDMI-1
[     8.074] (II) modeset(0): EDID for output DP-1
[     8.074] (II) modeset(0): EDID for output HDMI-2
[     8.101] (II) modeset(0): EDID for output HDMI-3
[     8.101] (II) modeset(0): Manufacturer: DEL  Model: 424a  Serial#: 1178097496
[     8.101] (II) modeset(0): Year: 2023  Week: 3
[     8.101] (II) modeset(0): EDID Version: 1.3
[     8.101] (II) modeset(0): Digital Display Input
[     8.101] (II) modeset(0): Max Image Size [cm]: horiz.: 53  vert.: 30
[     8.101] (II) modeset(0): Gamma: 2.20
[     8.101] (II) modeset(0): DPMS capabilities: StandBy Suspend Off
[     8.101] (II) modeset(0): Supported color encodings: RGB 4:4:4 YCrCb 4:4:4 
[     8.101] (II) modeset(0): First detailed timing is preferred mode
[     8.101] (II) modeset(0): redX: 0.657 redY: 0.330   greenX: 0.321 greenY: 0.624
[     8.101] (II) modeset(0): blueX: 0.154 blueY: 0.070   whiteX: 0.313 whiteY: 0.329
[     8.101] (II) modeset(0): Supported established timings:
[     8.101] (II) modeset(0): 720x400@70Hz
[     8.101] (II) modeset(0): 640x480@60Hz
[     8.101] (II) modeset(0): 640x480@75Hz
[     8.101] (II) modeset(0): 800x600@60Hz
[     8.101] (II) modeset(0): 800x600@75Hz
[     8.101] (II) modeset(0): 1024x768@60Hz
[     8.101] (II) modeset(0): 1024x768@75Hz
[     8.101] (II) modeset(0): 1280x1024@75Hz
[     8.101] (II) modeset(0): Manufacturer's mask: 0
[     8.101] (II) modeset(0): Supported standard timings:
[     8.101] (II) modeset(0): #0: hsize: 1152  vsize 864  refresh: 75  vid: 20337
[     8.101] (II) modeset(0): #1: hsize: 1280  vsize 1024  refresh: 60  vid: 32897
[     8.101] (II) modeset(0): #2: hsize: 1600  vsize 900  refresh: 60  vid: 49321
[     8.101] (II) modeset(0): #3: hsize: 1920  vsize 1080  refresh: 60  vid: 49361
[     8.101] (II) modeset(0): Supported detailed timing:
[     8.101] (II) modeset(0): clock: 148.5 MHz   Image Size:  527 x 296 mm
[     8.101] (II) modeset(0): h_active: 1920  h_sync: 2008  h_sync_end 2052 h_blank_end 2200 h_border: 0
[     8.101] (II) modeset(0): v_active: 1080  v_sync: 1084  v_sync_end 1089 v_blanking: 1125 v_border: 0
[     8.101] (II) modeset(0): Serial No: J2899V3
[     8.101] (II) modeset(0): Monitor name: DELL SE2422H
[     8.101] (II) modeset(0): Ranges: V min: 48 V max: 75 Hz, H min: 30 H max: 83 kHz, PixClock max 185 MHz
[     8.101] (II) modeset(0): Supported detailed timing:
[     8.101] (II) modeset(0): clock: 174.5 MHz   Image Size:  527 x 296 mm
[     8.101] (II) modeset(0): h_active: 1920  h_sync: 1968  h_sync_end 2000 h_blank_end 2080 h_border: 0
[     8.101] (II) modeset(0): v_active: 1080  v_sync: 1083  v_sync_end 1088 v_blanking: 1119 v_border: 0
[     8.101] (II) modeset(0): Supported detailed timing:
[     8.101] (II) modeset(0): clock: 74.2 MHz   Image Size:  527 x 296 mm
[     8.101] (II) modeset(0): h_active: 1920  h_sync: 2008  h_sync_end 2052 h_blank_end 2200 h_border: 0
[     8.101] (II) modeset(0): v_active: 540  v_sync: 542  v_sync_end 547 v_blanking: 562 v_border: 0
[     8.101] (II) modeset(0): Supported detailed timing:
[     8.101] (II) modeset(0): clock: 74.2 MHz   Image Size:  527 x 296 mm
[     8.101] (II) modeset(0): h_active: 1280  h_sync: 1390  h_sync_end 1430 h_blank_end 1650 h_border: 0
[     8.101] (II) modeset(0): v_active: 720  v_sync: 725  v_sync_end 730 v_blanking: 750 v_border: 0
[     8.101] (II) modeset(0): Supported detailed timing:
[     8.101] (II) modeset(0): clock: 27.0 MHz   Image Size:  527 x 296 mm
[     8.101] (II) modeset(0): h_active: 720  h_sync: 736  h_sync_end 798 h_blank_end 858 h_border: 0
[     8.101] (II) modeset(0): v_active: 480  v_sync: 489  v_sync_end 495 v_blanking: 525 v_border: 0
[     8.101] (II) modeset(0): Number of EDID sections to follow: 1
[     8.101] (II) modeset(0): EDID (in hex):
[     8.101] (II) modeset(0): 	00ffffffffffff0010ac4a4258573846
[     8.101] (II) modeset(0): 	0321010380351e78ea6785a854529f27
[     8.101] (II) modeset(0): 	125054a54b00714f8180a9c0d1c00101
[     8.101] (II) modeset(0): 	010101010101023a801871382d40582c
[     8.101] (II) modeset(0): 	45000f282100001e000000ff004a3238
[     8.101] (II) modeset(0): 	393956330a2020202020000000fc0044
[     8.101] (II) modeset(0): 	454c4c20534532343232480a000000fd
[     8.101] (II) modeset(0): 	00304b1e5312000a2020202020200193
[     8.101] (II) modeset(0): 	02031fb14b9005141f04131211030201
[     8.101] (II) modeset(0): 	65030c001000681a00000101304b002a
[     8.101] (II) modeset(0): 	4480a070382740302035000f28210000
[     8.101] (II) modeset(0): 	1a011d8018711c1620582c25000f2821
[     8.101] (II) modeset(0): 	00009e011d007251d01e206e2855000f
[     8.101] (II) modeset(0): 	282100001e8c0ad08a20e02d10103e96
[     8.101] (II) modeset(0): 	000f2821000018000000000000000000
[     8.101] (II) modeset(0): 	000000000000000000000000000000d8
[     8.102] (II) modeset(0): Printing probed modes for output HDMI-3
[     8.102] (II) modeset(0): Modeline "1920x1080"x60.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[     8.102] (II) modeset(0): Modeline "1920x1080"x75.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[     8.102] (II) modeset(0): Modeline "1920x1080"x50.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[     8.102] (II) modeset(0): Modeline "1920x1080"x59.9  148.35  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.4 kHz e)
[     8.102] (II) modeset(0): Modeline "1600x900"x60.0  108.00  1600 1624 1704 1800  900 901 904 1000 +hsync +vsync (60.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1280x1024"x75.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1280x1024"x60.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1152x864"x75.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[     8.102] (II) modeset(0): Modeline "1280x720"x60.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1280x720"x50.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[     8.102] (II) modeset(0): Modeline "1280x720"x59.9   74.18  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1024x768"x75.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[     8.102] (II) modeset(0): Modeline "1024x768"x60.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[     8.102] (II) modeset(0): Modeline "800x600"x75.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[     8.102] (II) modeset(0): Modeline "800x600"x60.3   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[     8.102] (II) modeset(0): Modeline "720x576"x50.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[     8.102] (II) modeset(0): Modeline "720x480"x60.0   27.03  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     8.102] (II) modeset(0): Modeline "720x480"x59.9   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     8.102] (II) modeset(0): Modeline "640x480"x75.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[     8.102] (II) modeset(0): Modeline "640x480"x60.0   25.20  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     8.102] (II) modeset(0): Modeline "640x480"x59.9   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     8.102] (II) modeset(0): Modeline "720x400"x70.1   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[     8.102] (II) modeset(0): EDID for output DP-2
[     8.102] (II) modeset(0): EDID for output HDMI-4
[     8.102] (II) modeset(0): Output HDMI-1 disconnected
[     8.102] (II) modeset(0): Output DP-1 disconnected
[     8.102] (II) modeset(0): Output HDMI-2 disconnected
[     8.102] (II) modeset(0): Output HDMI-3 connected
[     8.102] (II) modeset(0): Output DP-2 disconnected
[     8.102] (II) modeset(0): Output HDMI-4 disconnected
[     8.102] (II) modeset(0): Using exact sizes for initial modes
[     8.102] (II) modeset(0): Output HDMI-3 using initial mode 1920x1080 +0+0
[     8.102] (==) modeset(0): Using gamma correction (1.0, 1.0, 1.0)
[     8.102] (==) modeset(0): DPI set to (96, 96)
[     8.102] (II) Loading sub module "fb"
[     8.102] (II) LoadModule: "fb"
[     8.102] (II) Module "fb" already built-in
[     8.102] (II) UnloadModule: "fbdev"
[     8.102] (II) Unloading fbdev
[     8.102] (II) UnloadSubModule: "fbdevhw"
[     8.102] (II) Unloading fbdevhw
[     8.102] (II) UnloadModule: "vesa"
[     8.102] (II) Unloading vesa
[     8.142] (==) modeset(0): Backing store enabled
[     8.142] (==) modeset(0): Silken mouse enabled
[     8.288] (II) modeset(0): Initializing kms color map for depth 24, 8 bpc.
[     8.288] (==) modeset(0): DPMS enabled
[     8.288] (II) modeset(0): [DRI2] Setup complete
[     8.288] (II) modeset(0): [DRI2]   DRI driver: iris
[     8.288] (II) modeset(0): [DRI2]   VDPAU driver: va_gl
[     8.288] (II) Initializing extension Generic Event Extension
[     8.288] (II) Initializing extension SHAPE
[     8.288] (II) Initializing extension MIT-SHM
[     8.288] (II) Initializing extension XInputExtension
[     8.289] (II) Initializing extension XTEST
[     8.289] (II) Initializing extension BIG-REQUESTS
[     8.289] (II) Initializing extension SYNC
[     8.289] (II) Initializing extension XKEYBOARD
[     8.289] (II) Initializing extension XC-MISC
[     8.289] (II) Initializing extension SECURITY
[     8.289] (II) Initializing extension XFIXES
[     8.289] (II) Initializing extension RENDER
[     8.289] (II) Initializing extension RANDR
[     8.289] (II) Initializing extension COMPOSITE
[     8.290] (II) Initializing extension DAMAGE
[     8.290] (II) Initializing extension MIT-SCREEN-SAVER
[     8.290] (II) Initializing extension DOUBLE-BUFFER
[     8.290] (II) Initializing extension RECORD
[     8.290] (II) Initializing extension DPMS
[     8.290] (II) Initializing extension Present
[     8.290] (II) Initializing extension DRI3
[     8.290] (II) Initializing extension X-Resource
[     8.290] (II) Initializing extension XVideo
[     8.290] (II) Initializing extension XVideo-MotionCompensation
[     8.290] (II) Initializing extension SELinux
[     8.290] (II) SELinux: Disabled on system
[     8.290] (II) Initializing extension GLX
[     8.294] (II) AIGLX: Loaded and initialized iris
[     8.294] (II) GLX: Initialized DRI2 GL provider for screen 0
[     8.294] (II) Initializing extension XFree86-VidModeExtension
[     8.294] (II) Initializing extension XFree86-DGA
[     8.294] (II) Initializing extension XFree86-DRI
[     8.294] (II) Initializing extension DRI2
[     8.295] (II) modeset(0): Damage tracking initialized
[     8.295] (II) modeset(0): Setting screen physical size to 508 x 285
[     8.347] (II) config/udev: Adding input device Power Button (/dev/input/event2)
[     8.347] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.347] (II) LoadModule: "libinput"
[     8.347] (II) Loading /usr/lib/xorg/modules/input/libinput_drv.so
[     8.351] (II) Module libinput: vendor="X.Org Foundation"
[     8.351] 	compiled for 1.20.14, module version = 1.2.1
[     8.351] 	Module class: X.Org XInput Driver
[     8.351] 	ABI class: X.Org XInput driver, version 24.1
[     8.351] (II) Using input driver 'libinput' for 'Power Button'
[     8.352] (II) systemd-logind: got fd for /dev/input/event2 13:66 fd 28 paused 0
[     8.352] (**) Power Button: always reports core events
[     8.352] (**) Option "Device" "/dev/input/event2"
[     8.361] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.361] (II) event2  - Power Button: device is a keyboard
[     8.361] (II) event2  - Power Button: device removed
[     8.361] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXPWRBN:00/input/input2/event2"
[     8.362] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 6)
[     8.362] (**) Option "xkb_model" "pc105"
[     8.362] (**) Option "xkb_layout" "cn"
[     8.372] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.372] (II) event2  - Power Button: device is a keyboard
[     8.372] (II) config/udev: Adding input device Video Bus (/dev/input/event8)
[     8.372] (**) Video Bus: Applying InputClass "libinput keyboard catchall"
[     8.372] (II) Using input driver 'libinput' for 'Video Bus'
[     8.373] (II) systemd-logind: got fd for /dev/input/event8 13:72 fd 31 paused 0
[     8.373] (**) Video Bus: always reports core events
[     8.373] (**) Option "Device" "/dev/input/event8"
[     8.373] (II) event8  - Video Bus: is tagged by udev as: Keyboard
[     8.373] (II) event8  - Video Bus: device is a keyboard
[     8.373] (II) event8  - Video Bus: device removed
[     8.373] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0A08:00/LNXVIDEO:00/input/input8/event8"
[     8.373] (II) XINPUT: Adding extended input device "Video Bus" (type: KEYBOARD, id 7)
[     8.373] (**) Option "xkb_model" "pc105"
[     8.373] (**) Option "xkb_layout" "cn"
[     8.374] (II) event8  - Video Bus: is tagged by udev as: Keyboard
[     8.374] (II) event8  - Video Bus: device is a keyboard
[     8.374] (II) config/udev: Adding input device Power Button (/dev/input/event1)
[     8.374] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.374] (II) Using input driver 'libinput' for 'Power Button'
[     8.374] (II) systemd-logind: got fd for /dev/input/event1 13:65 fd 32 paused 0
[     8.374] (**) Power Button: always reports core events
[     8.374] (**) Option "Device" "/dev/input/event1"
[     8.375] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.375] (II) event1  - Power Button: device is a keyboard
[     8.375] (II) event1  - Power Button: device removed
[     8.375] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0C:00/input/input1/event1"
[     8.375] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 8)
[     8.375] (**) Option "xkb_model" "pc105"
[     8.375] (**) Option "xkb_layout" "cn"
[     8.375] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.375] (II) event1  - Power Button: device is a keyboard
[     8.376] (II) config/udev: Adding input device Sleep Button (/dev/input/event0)
[     8.376] (**) Sleep Button: Applying InputClass "libinput keyboard catchall"
[     8.376] (II) Using input driver 'libinput' for 'Sleep Button'
[     8.376] (II) systemd-logind: got fd for /dev/input/event0 13:64 fd 33 paused 0
[     8.376] (**) Sleep Button: always reports core events
[     8.376] (**) Option "Device" "/dev/input/event0"
[     8.376] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.376] (II) event0  - Sleep Button: device is a keyboard
[     8.376] (II) event0  - Sleep Button: device removed
[     8.376] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0E:00/input/input0/event0"
[     8.376] (II) XINPUT: Adding extended input device "Sleep Button" (type: KEYBOARD, id 9)
[     8.376] (**) Option "xkb_model" "pc105"
[     8.377] (**) Option "xkb_layout" "cn"
[     8.377] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.377] (II) event0  - Sleep Button: device is a keyboard
[     8.377] (II) config/udev: Adding input device Dell KB216 Wired Keyboard (/dev/input/event3)
[     8.377] (**) Dell KB216 Wired Keyboard: Applying InputClass "libinput keyboard catchall"
[     8.377] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard'
[     8.378] (II) systemd-logind: got fd for /dev/input/event3 13:67 fd 34 paused 0
[     8.378] (**) Dell KB216 Wired Keyboard: always reports core events
[     8.378] (**) Option "Device" "/dev/input/event3"
[     8.379] (II) event3  - Dell KB216 Wired Keyboard: is tagged by udev as: Keyboard
[     8.379] (II) event3  - Dell KB216 Wired Keyboard: device is a keyboard
[     8.379] (II) event3  - Dell KB216 Wired Keyboard: device removed
[     8.379] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.0/0003:413C:2113.0001/input/input3/event3"
[     8.379] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard" (type: KEYBOARD, id 10)
[     8.379] (**) Option "xkb_model" "pc105"
[     8.379] (**) Option "xkb_layout" "cn"
[     8.379] (II) event3  - Dell KB216 Wired Keyboard: is tagged by udev as: Keyboard
[     8.379] (II) event3  - Dell KB216 Wired Keyboard: device is a keyboard
[     8.380] (II) config/udev: Adding input device Dell KB216 Wired Keyboard Consumer Control (/dev/input/event4)
[     8.380] (**) Dell KB216 Wired Keyboard Consumer Control: Applying InputClass "libinput keyboard catchall"
[     8.380] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard Consumer Control'
[     8.380] (II) systemd-logind: got fd for /dev/input/event4 13:68 fd 35 paused 0
[     8.380] (**) Dell KB216 Wired Keyboard Consumer Control: always reports core events
[     8.380] (**) Option "Device" "/dev/input/event4"
[     8.381] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: is tagged by udev as: Keyboard
[     8.382] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device is a keyboard
[     8.382] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device removed
[     8.382] (II) libinput: Dell KB216 Wired Keyboard Consumer Control: needs a virtual subdevice
[     8.382] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input4/event4"
[     8.382] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard Consumer Control" (type: MOUSE, id 11)
[     8.382] (**) Option "AccelerationScheme" "none"
[     8.382] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) selected scheme none/0
[     8.382] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) acceleration factor: 2.000
[     8.382] (**) Dell KB216 Wired Keyboard Consumer Control: (accel) acceleration threshold: 4
[     8.383] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: is tagged by udev as: Keyboard
[     8.383] (II) event4  - Dell KB216 Wired Keyboard Consumer Control: device is a keyboard
[     8.383] (II) config/udev: Adding input device Dell KB216 Wired Keyboard System Control (/dev/input/event5)
[     8.383] (**) Dell KB216 Wired Keyboard System Control: Applying InputClass "libinput keyboard catchall"
[     8.383] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard System Control'
[     8.384] (II) systemd-logind: got fd for /dev/input/event5 13:69 fd 36 paused 0
[     8.384] (**) Dell KB216 Wired Keyboard System Control: always reports core events
[     8.384] (**) Option "Device" "/dev/input/event5"
[     8.385] (II) event5  - Dell KB216 Wired Keyboard System Control: is tagged by udev as: Keyboard
[     8.385] (II) event5  - Dell KB216 Wired Keyboard System Control: device is a keyboard
[     8.385] (II) event5  - Dell KB216 Wired Keyboard System Control: device removed
[     8.385] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input5/event5"
[     8.385] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard System Control" (type: KEYBOARD, id 12)
[     8.385] (**) Option "xkb_model" "pc105"
[     8.385] (**) Option "xkb_layout" "cn"
[     8.386] (II) event5  - Dell KB216 Wired Keyboard System Control: is tagged by udev as: Keyboard
[     8.386] (II) event5  - Dell KB216 Wired Keyboard System Control: device is a keyboard
[     8.387] (II) config/udev: Adding input device PixArt Dell MS116 USB Optical Mouse (/dev/input/event6)
[     8.387] (**) PixArt Dell MS116 USB Optical Mouse: Applying InputClass "libinput pointer catchall"
[     8.387] (II) Using input driver 'libinput' for 'PixArt Dell MS116 USB Optical Mouse'
[     8.387] (II) systemd-logind: got fd for /dev/input/event6 13:70 fd 37 paused 0
[     8.388] (**) PixArt Dell MS116 USB Optical Mouse: always reports core events
[     8.388] (**) Option "Device" "/dev/input/event6"
[     8.388] (II) event6  - PixArt Dell MS116 USB Optical Mouse: is tagged by udev as: Mouse
[     8.388] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device set to 1000 DPI
[     8.388] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device is a pointer
[     8.388] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device removed
[     8.388] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-8/1-8:1.0/0003:413C:301A.0003/input/input6/event6"
[     8.388] (II) XINPUT: Adding extended input device "PixArt Dell MS116 USB Optical Mouse" (type: MOUSE, id 13)
[     8.388] (**) Option "AccelerationScheme" "none"
[     8.388] (**) PixArt Dell MS116 USB Optical Mouse: (accel) selected scheme none/0
[     8.388] (**) PixArt Dell MS116 USB Optical Mouse: (accel) acceleration factor: 2.000
[     8.388] (**) PixArt Dell MS116 USB Optical Mouse: (accel) acceleration threshold: 4
[     8.389] (II) event6  - PixArt Dell MS116 USB Optical Mouse: is tagged by udev as: Mouse
[     8.389] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device set to 1000 DPI
[     8.389] (II) event6  - PixArt Dell MS116 USB Optical Mouse: device is a pointer
[     8.390] (II) config/udev: Adding input device PixArt Dell MS116 USB Optical Mouse (/dev/input/mouse0)
[     8.390] (II) No input driver specified, ignoring this device.
[     8.390] (II) This device may have been added with another device file.
[     8.390] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=7 (/dev/input/event10)
[     8.390] (II) No input driver specified, ignoring this device.
[     8.390] (II) This device may have been added with another device file.
[     8.390] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=8 (/dev/input/event11)
[     8.390] (II) No input driver specified, ignoring this device.
[     8.390] (II) This device may have been added with another device file.
[     8.390] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=9 (/dev/input/event12)
[     8.390] (II) No input driver specified, ignoring this device.
[     8.390] (II) This device may have been added with another device file.
[     8.390] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=3 (/dev/input/event9)
[     8.390] (II) No input driver specified, ignoring this device.
[     8.390] (II) This device may have been added with another device file.
[     8.390] (II) config/udev: Adding input device Intel HID events (/dev/input/event7)
[     8.390] (**) Intel HID events: Applying InputClass "libinput keyboard catchall"
[     8.390] (II) Using input driver 'libinput' for 'Intel HID events'
[     8.391] (II) systemd-logind: got fd for /dev/input/event7 13:71 fd 38 paused 0
[     8.391] (**) Intel HID events: always reports core events
[     8.391] (**) Option "Device" "/dev/input/event7"
[     8.391] (II) event7  - Intel HID events: is tagged by udev as: Keyboard
[     8.391] (II) event7  - Intel HID events: device is a keyboard
[     8.391] (II) event7  - Intel HID events: device removed
[     8.391] (**) Option "config_info" "udev:/sys/devices/platform/INTC1070:00/input/input7/event7"
[     8.391] (II) XINPUT: Adding extended input device "Intel HID events" (type: KEYBOARD, id 14)
[     8.391] (**) Option "xkb_model" "pc105"
[     8.391] (**) Option "xkb_layout" "cn"
[     8.392] (II) event7  - Intel HID events: is tagged by udev as: Keyboard
[     8.392] (II) event7  - Intel HID events: device is a keyboard
[     8.404] (**) Dell KB216 Wired Keyboard Consumer Control: Applying InputClass "libinput keyboard catchall"
[     8.404] (II) Using input driver 'libinput' for 'Dell KB216 Wired Keyboard Consumer Control'
[     8.404] (II) systemd-logind: returning pre-existing fd for /dev/input/event4 13:68
[     8.404] (**) Dell KB216 Wired Keyboard Consumer Control: always reports core events
[     8.404] (**) Option "Device" "/dev/input/event4"
[     8.404] (II) libinput: Dell KB216 Wired Keyboard Consumer Control: is a virtual subdevice
[     8.404] (**) Option "config_info" "udev:/sys/devices/pci0000:00/0000:00:14.0/usb1/1-7/1-7:1.1/0003:413C:2113.0002/input/input4/event4"
[     8.404] (II) XINPUT: Adding extended input device "Dell KB216 Wired Keyboard Consumer Control" (type: KEYBOARD, id 15)
[     8.404] (**) Option "xkb_model" "pc105"
[     8.404] (**) Option "xkb_layout" "cn"
[     9.104] (II) modeset(0): EDID vendor "DEL", prod id 16970
[     9.104] (II) modeset(0): Using EDID range info for horizontal sync
[     9.104] (II) modeset(0): Using EDID range info for vertical refresh
[     9.104] (II) modeset(0): Printing DDC gathered Modelines:
[     9.104] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[     9.104] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[     9.104] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[     9.104] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[     9.104] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[     9.104] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[     9.104] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[     9.104] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[     9.104] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[     9.104] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[     9.104] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[     9.104] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[     9.104] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[     9.104] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[     9.104] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[     9.104] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[     9.104] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[     9.104] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[     9.104] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[     9.104] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[    10.121] (II) modeset(0): EDID vendor "DEL", prod id 16970
[    10.121] (II) modeset(0): Using hsync ranges from config file
[    10.121] (II) modeset(0): Using vrefresh ranges from config file
[    10.121] (II) modeset(0): Printing DDC gathered Modelines:
[    10.121] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[    10.121] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[    10.121] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[    10.121] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[    10.121] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[    10.121] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[    10.121] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[    10.121] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[    10.121] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[    10.121] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[    10.121] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[    10.121] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[    10.121] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[    10.121] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[    10.121] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[    10.121] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[    10.121] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[    10.121] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[    10.121] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[    10.121] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
[ 70310.489] (II) modeset(0): EDID vendor "DEL", prod id 16970
[ 70310.489] (II) modeset(0): Using hsync ranges from config file
[ 70310.489] (II) modeset(0): Using vrefresh ranges from config file
[ 70310.489] (II) modeset(0): Printing DDC gathered Modelines:
[ 70310.489] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2008 2052 2200  1080 1084 1089 1125 +hsync +vsync (67.5 kHz eP)
[ 70310.489] (II) modeset(0): Modeline "1920x1080"x0.0  174.50  1920 1968 2000 2080  1080 1083 1088 1119 +hsync -vsync (83.9 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2008 2052 2200  1080 1084 1094 1125 interlace +hsync +vsync (33.8 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1390 1430 1650  720 725 730 750 +hsync +vsync (45.0 kHz e)
[ 70310.489] (II) modeset(0): Modeline "720x480"x0.0   27.00  720 736 798 858  480 489 495 525 -hsync -vsync (31.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1920x1080i"x0.0   74.25  1920 2448 2492 2640  1080 1084 1094 1125 interlace +hsync +vsync (28.1 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1920x1080"x0.0  148.50  1920 2448 2492 2640  1080 1084 1089 1125 +hsync +vsync (56.2 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1280x720"x0.0   74.25  1280 1720 1760 1980  720 725 730 750 +hsync +vsync (37.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "720x576"x0.0   27.00  720 732 796 864  576 581 586 625 -hsync -vsync (31.2 kHz e)
[ 70310.489] (II) modeset(0): Modeline "640x480"x0.0   25.18  640 656 752 800  480 490 492 525 -hsync -vsync (31.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "800x600"x0.0   40.00  800 840 968 1056  600 601 605 628 +hsync +vsync (37.9 kHz e)
[ 70310.489] (II) modeset(0): Modeline "640x480"x0.0   31.50  640 656 720 840  480 481 484 500 -hsync -vsync (37.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "720x400"x0.0   28.32  720 738 846 900  400 412 414 449 -hsync +vsync (31.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1280x1024"x0.0  135.00  1280 1296 1440 1688  1024 1025 1028 1066 +hsync +vsync (80.0 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1024x768"x0.0   78.75  1024 1040 1136 1312  768 769 772 800 +hsync +vsync (60.0 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1024x768"x0.0   65.00  1024 1048 1184 1344  768 771 777 806 -hsync -vsync (48.4 kHz e)
[ 70310.489] (II) modeset(0): Modeline "800x600"x0.0   49.50  800 816 896 1056  600 601 604 625 +hsync +vsync (46.9 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1152x864"x0.0  108.00  1152 1216 1344 1600  864 865 868 900 +hsync +vsync (67.5 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1280x1024"x0.0  108.00  1280 1328 1440 1688  1024 1025 1028 1066 +hsync +vsync (64.0 kHz e)
[ 70310.489] (II) modeset(0): Modeline "1600x900"x60.0  119.00  1600 1696 1864 2128  900 901 904 932 -hsync +vsync (55.9 kHz e)
